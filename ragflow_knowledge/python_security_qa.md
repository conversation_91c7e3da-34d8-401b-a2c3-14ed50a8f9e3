# Python安全编码Q&A知识库

## Q: 如何防止Python代码中的SQL注入攻击？

**A:** SQL注入是P0级别的严重安全问题，必须立即修复。

**问题代码示例：**
```python
# 危险：直接字符串拼接
user_id = request.get('user_id')
query = f"SELECT * FROM users WHERE id = {user_id}"
cursor.execute(query)

# 危险：使用%格式化
query = "SELECT * FROM users WHERE name = '%s'" % username
cursor.execute(query)
```

**正确做法：**
```python
# 安全：使用参数化查询
cursor.execute("SELECT * FROM users WHERE id = %s", (user_id,))

# 安全：使用ORM
from django.contrib.auth.models import User
user = User.objects.get(id=user_id)

# 安全：使用SQLAlchemy
user = session.query(User).filter(User.id == user_id).first()
```

**检测关键词：** SQL注入, 字符串拼接, f-string, %格式化, execute
**风险等级：** P0 - 严重
**修复优先级：** 必须修复

---

## Q: Python代码中如何安全地处理用户密码？

**A:** 密码处理不当是P0级别安全问题，绝不能明文存储或传输。

**问题代码示例：**
```python
# 危险：明文存储密码
user_password = "123456"
user_data = {"username": "john", "password": user_password}

# 危险：使用弱哈希算法
import hashlib
password_hash = hashlib.md5(password.encode()).hexdigest()

# 危险：密码记录到日志
logger.info(f"User login: {username}, password: {password}")
```

**正确做法：**
```python
import bcrypt
import secrets

# 安全：使用bcrypt哈希密码
def hash_password(password: str) -> str:
    salt = bcrypt.gensalt()
    return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')

# 安全：验证密码
def verify_password(password: str, hashed: str) -> bool:
    return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))

# 安全：生成安全令牌
def generate_secure_token(length: int = 32) -> str:
    return secrets.token_urlsafe(length)

# 安全：日志记录（不包含敏感信息）
logger.info(f"User {username} login attempt")
```

**检测关键词：** 密码安全, bcrypt, 明文密码, md5, sha1, 日志泄露
**风险等级：** P0 - 严重
**修复优先级：** 必须修复

---

## Q: 如何防止Python代码中的命令注入攻击？

**A:** 命令注入是P0级别严重问题，可能导致系统完全被控制。

**问题代码示例：**
```python
import os
import subprocess

# 危险：直接执行用户输入
user_input = request.get('command')
os.system(user_input)

# 危险：shell=True with user input
filename = request.get('filename')
subprocess.call(f"cat {filename}", shell=True)

# 危险：eval执行用户代码
user_code = request.get('code')
result = eval(user_code)
```

**正确做法：**
```python
import subprocess
import shlex

# 安全：使用参数列表
def safe_list_files(directory: str):
    # 验证目录路径
    if not os.path.isdir(directory):
        raise ValueError("Invalid directory")
    subprocess.call(["ls", "-l", directory])

# 安全：白名单验证
ALLOWED_COMMANDS = ["ls", "cat", "grep", "head", "tail"]

def safe_execute(command: str, args: list):
    if command not in ALLOWED_COMMANDS:
        raise ValueError(f"Command {command} not allowed")
    
    # 转义参数
    safe_args = [shlex.quote(arg) for arg in args]
    subprocess.call([command] + safe_args)

# 安全：避免eval，使用ast.literal_eval
import ast
def safe_eval(expression: str):
    try:
        return ast.literal_eval(expression)
    except (ValueError, SyntaxError):
        raise ValueError("Invalid expression")
```

**检测关键词：** 命令注入, os.system, subprocess, shell=True, eval, exec
**风险等级：** P0 - 严重
**修复优先级：** 必须修复

---

## Q: Python代码中如何安全地处理文件上传？

**A:** 不安全的文件上传可能导致P0级别的安全问题，包括代码执行和系统入侵。

**问题代码示例：**
```python
# 危险：不验证文件类型
@app.route('/upload', methods=['POST'])
def upload_file():
    file = request.files['file']
    file.save(f"uploads/{file.filename}")  # 直接保存

# 危险：只检查文件扩展名
def is_allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ['jpg', 'png']
```

**正确做法：**
```python
import os
import uuid
import magic
from werkzeug.utils import secure_filename

ALLOWED_EXTENSIONS = {'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif'}
ALLOWED_MIME_TYPES = {
    'image/jpeg', 'image/png', 'image/gif',
    'application/pdf', 'text/plain'
}
MAX_FILE_SIZE = 16 * 1024 * 1024  # 16MB

def validate_file_upload(file):
    # 检查文件是否存在
    if not file or file.filename == '':
        raise ValueError("No file selected")
    
    # 检查文件大小
    file.seek(0, os.SEEK_END)
    file_size = file.tell()
    file.seek(0)
    if file_size > MAX_FILE_SIZE:
        raise ValueError("File too large")
    
    # 检查文件扩展名
    if not ('.' in file.filename and 
            file.filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS):
        raise ValueError("File type not allowed")
    
    return True

def secure_file_upload(file, upload_folder: str) -> str:
    validate_file_upload(file)
    
    # 生成安全文件名
    filename = secure_filename(file.filename)
    unique_filename = f"{uuid.uuid4()}_{filename}"
    file_path = os.path.join(upload_folder, unique_filename)
    
    # 保存文件
    file.save(file_path)
    
    # 验证文件MIME类型
    mime = magic.Magic(mime=True)
    file_mime_type = mime.from_file(file_path)
    if file_mime_type not in ALLOWED_MIME_TYPES:
        os.remove(file_path)
        raise ValueError("Invalid file content")
    
    return unique_filename
```

**检测关键词：** 文件上传, 文件验证, MIME类型, secure_filename, 文件大小限制
**风险等级：** P0 - 严重
**修复优先级：** 必须修复

---

## Q: 如何防止Python代码中的XSS攻击？

**A:** XSS攻击是P1级别重要问题，可能导致用户数据泄露和会话劫持。

**问题代码示例：**
```python
# 危险：直接输出用户数据
@app.route('/user/<username>')
def show_user(username):
    return f"<h1>Welcome {username}</h1>"

# 危险：在模板中不转义
# template.html: <div>Hello {{ username }}</div>
```

**正确做法：**
```python
from markupsafe import escape
from flask import render_template_string

# 安全：HTML转义
@app.route('/user/<username>')
def show_user(username):
    safe_username = escape(username)
    return f"<h1>Welcome {safe_username}</h1>"

# 安全：使用模板引擎自动转义
@app.route('/user/<username>')
def show_user_template(username):
    return render_template('user.html', username=username)

# 安全：内容安全策略
from flask_talisman import Talisman

csp = {
    'default-src': "'self'",
    'script-src': "'self'",
    'style-src': "'self' 'unsafe-inline'",
    'img-src': "'self' data: https:",
}

Talisman(app, content_security_policy=csp)
```

**检测关键词：** XSS, HTML转义, 模板注入, CSP, 内容安全策略
**风险等级：** P1 - 重要
**修复优先级：** 建议修复

---

## Q: Python代码中如何安全地处理JSON数据？

**A:** 不安全的JSON处理可能导致P1级别的安全问题。

**问题代码示例：**
```python
import json
import pickle

# 危险：反序列化不可信数据
user_data = pickle.loads(request.data)

# 危险：不验证JSON结构
data = json.loads(request.data)
username = data['username']  # 可能KeyError
```

**正确做法：**
```python
import json
from typing import Dict, Any

def safe_json_parse(json_string: str, max_size: int = 1024*1024) -> Dict[str, Any]:
    # 检查数据大小
    if len(json_string) > max_size:
        raise ValueError("JSON data too large")
    
    try:
        data = json.loads(json_string)
        if not isinstance(data, dict):
            raise ValueError("JSON must be an object")
        return data
    except json.JSONDecodeError as e:
        raise ValueError(f"Invalid JSON: {e}")

def validate_user_data(data: Dict[str, Any]) -> Dict[str, str]:
    required_fields = ['username', 'email']
    validated_data = {}
    
    for field in required_fields:
        if field not in data:
            raise ValueError(f"Missing required field: {field}")
        
        value = data[field]
        if not isinstance(value, str) or not value.strip():
            raise ValueError(f"Invalid {field}")
        
        validated_data[field] = value.strip()
    
    return validated_data

# 安全使用示例
@app.route('/api/user', methods=['POST'])
def create_user():
    try:
        json_data = safe_json_parse(request.get_data(as_text=True))
        user_data = validate_user_data(json_data)
        # 处理验证后的数据
        return {"status": "success"}
    except ValueError as e:
        return {"error": str(e)}, 400
```

**检测关键词：** JSON安全, 反序列化, pickle, 数据验证, 输入验证
**风险等级：** P1 - 重要
**修复优先级：** 建议修复

---

## Q: 如何在Python代码中安全地使用正则表达式？

**A:** 不安全的正则表达式可能导致P2级别的性能问题（ReDoS攻击）。

**问题代码示例：**
```python
import re

# 危险：可能导致ReDoS的正则表达式
email_pattern = r'^(a+)+b$'
if re.match(email_pattern, user_input):
    print("Valid email")

# 危险：复杂的嵌套量词
phone_pattern = r'^(\d+)*(\d+)*$'
```

**正确做法：**
```python
import re
import signal
from contextlib import contextmanager

# 安全：使用简单、高效的正则表达式
EMAIL_PATTERN = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
PHONE_PATTERN = re.compile(r'^\+?1?[0-9]{10,15}$')

@contextmanager
def timeout_regex(seconds: int):
    """为正则表达式添加超时保护"""
    def timeout_handler(signum, frame):
        raise TimeoutError("Regex execution timeout")
    
    old_handler = signal.signal(signal.SIGALRM, timeout_handler)
    signal.alarm(seconds)
    try:
        yield
    finally:
        signal.alarm(0)
        signal.signal(signal.SIGALRM, old_handler)

def safe_regex_match(pattern: re.Pattern, text: str, timeout: int = 1) -> bool:
    """安全的正则表达式匹配"""
    if len(text) > 10000:  # 限制输入长度
        return False
    
    try:
        with timeout_regex(timeout):
            return bool(pattern.match(text))
    except TimeoutError:
        return False

# 使用示例
def validate_email(email: str) -> bool:
    return safe_regex_match(EMAIL_PATTERN, email)

def validate_phone(phone: str) -> bool:
    return safe_regex_match(PHONE_PATTERN, phone)
```

**检测关键词：** 正则表达式, ReDoS, 性能攻击, 超时保护, 输入长度限制
**风险等级：** P2 - 一般
**修复优先级：** 建议优化
