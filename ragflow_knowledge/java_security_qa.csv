question,answer,category,severity,language,keywords,bad_example,good_example
"如何防止Java代码中的SQL注入攻击？","SQL注入是P0级别严重安全问题。防护措施：1. 使用PreparedStatement而非Statement；2. 使用JPA参数绑定；3. 对用户输入进行验证；4. 使用存储过程；5. 实施最小权限原则。","安全编码","P0","Java","SQL注入,PreparedStatement,JPA,参数绑定","// 危险：字符串拼接
String sql = ""SELECT * FROM users WHERE id = "" + userId;
Statement stmt = conn.createStatement();
ResultSet rs = stmt.executeQuery(sql);","// 安全：PreparedStatement
String sql = ""SELECT * FROM users WHERE id = ?"";
PreparedStatement stmt = conn.prepareStatement(sql);
stmt.setString(1, userId);
ResultSet rs = stmt.executeQuery();
// 安全：JPA
@Query(""SELECT u FROM User u WHERE u.id = :id"")
User findById(@Param(""id"") String id);"
"如何防止Java代码中的反序列化漏洞？","反序列化漏洞是P0级别严重问题，可能导致远程代码执行。防护措施：1. 避免反序列化不可信数据；2. 使用白名单验证类；3. 使用安全的序列化格式；4. 实现自定义反序列化验证。","安全编码","P0","Java","反序列化,ObjectInputStream,序列化安全,白名单","// 危险：直接反序列化
ObjectInputStream ois = new ObjectInputStream(inputStream);
Object obj = ois.readObject(); // 可能执行恶意代码","// 安全：白名单验证
public class SafeObjectInputStream extends ObjectInputStream {
    private Set<String> allowedClasses;
    
    @Override
    protected Class<?> resolveClass(ObjectStreamClass desc) 
            throws IOException, ClassNotFoundException {
        if (!allowedClasses.contains(desc.getName())) {
            throw new InvalidClassException(""Class not allowed"");
        }
        return super.resolveClass(desc);
    }
}"
"如何防止Java代码中的内存泄漏？","内存泄漏是P0级别严重问题。防护措施：1. 使用try-with-resources管理资源；2. 及时移除监听器；3. 使用WeakReference；4. 避免静态集合持有对象引用；5. 正确关闭连接和流。","安全编码","P0","Java","内存泄漏,try-with-resources,WeakReference,资源管理","// 危险：资源未关闭
FileInputStream fis = new FileInputStream(filename);
BufferedReader reader = new BufferedReader(new InputStreamReader(fis));
String content = reader.readLine();
return content; // 资源未关闭","// 安全：try-with-resources
try (FileInputStream fis = new FileInputStream(filename);
     BufferedReader reader = new BufferedReader(new InputStreamReader(fis))) {
    return reader.readLine();
}
// 安全：WeakReference避免内存泄漏
private List<WeakReference<EventListener>> listeners = new ArrayList<>();"
"如何防止Java代码中的线程安全问题？","线程安全问题是P0级别严重问题。解决方案：1. 使用synchronized关键字；2. 使用volatile确保可见性；3. 使用java.util.concurrent包；4. 避免共享可变状态；5. 使用线程安全的集合类。","安全编码","P0","Java","线程安全,synchronized,volatile,ConcurrentHashMap,原子操作","// 危险：非线程安全的单例
public class Singleton {
    private static Singleton instance;
    public static Singleton getInstance() {
        if (instance == null) {
            instance = new Singleton(); // 竞态条件
        }
        return instance;
    }
}","// 安全：线程安全的单例
public class Singleton {
    private static volatile Singleton instance;
    public static Singleton getInstance() {
        if (instance == null) {
            synchronized (Singleton.class) {
                if (instance == null) {
                    instance = new Singleton();
                }
            }
        }
        return instance;
    }
}"
"如何防止Java代码中的空指针异常？","空指针异常是P0级别严重问题。防护措施：1. 进行显式空值检查；2. 使用Optional处理可能为空的值；3. 使用@NonNull等注解；4. 使用Objects.requireNonNull验证；5. 初始化集合和对象。","安全编码","P0","Java","NullPointerException,Optional,空值检查,@NonNull","// 危险：未检查空值
public String processUser(User user) {
    return user.getName().toUpperCase(); // 可能NPE
}
// 危险：链式调用
public String getUserEmail(Long userId) {
    User user = userService.findById(userId);
    return user.getProfile().getEmail(); // 多个NPE风险
}","// 安全：空值检查
public String processUser(User user) {
    Objects.requireNonNull(user, ""User cannot be null"");
    String name = user.getName();
    return name != null ? name.toUpperCase() : """";
}
// 安全：使用Optional
public Optional<String> getUserEmail(Long userId) {
    return Optional.ofNullable(userService.findById(userId))
            .map(User::getProfile)
            .map(Profile::getEmail);
}"
"Java代码中如何正确处理异常？","异常处理不当是P1级别问题。最佳实践：1. 捕获具体异常类型；2. 记录异常信息到日志；3. 提供有意义的错误信息；4. 使用自定义异常；5. 在finally块中清理资源。","代码规范","P1","Java","异常处理,try-catch,日志记录,自定义异常,finally","// 危险：捕获过于宽泛的异常
try {
    complexOperation();
} catch (Exception e) {
    e.printStackTrace(); // 不应直接打印
}
// 危险：静默忽略异常
try {
    return Files.readString(Paths.get(""config.txt""));
} catch (IOException e) {
    return """"; // 静默忽略
}","// 正确：具体异常处理
try {
    complexOperation();
} catch (ValidationException e) {
    logger.error(""Data validation failed"", e);
    throw new ProcessingException(""Invalid data"", e);
} catch (IOException e) {
    logger.error(""IO error during processing"", e);
    throw new ProcessingException(""IO error"", e);
}
// 正确：自定义异常
public class ValidationException extends Exception {
    public ValidationException(String message, Throwable cause) {
        super(message, cause);
    }
}"
"Java代码中如何正确管理资源？","资源管理不当是P1级别问题。最佳实践：1. 使用try-with-resources自动管理；2. 实现AutoCloseable接口；3. 在finally块中确保释放；4. 使用资源池管理昂贵资源；5. 避免资源泄漏。","代码规范","P1","Java","资源管理,try-with-resources,AutoCloseable,finally,资源池","// 危险：手动管理资源
FileWriter writer = null;
try {
    writer = new FileWriter(filename);
    writer.write(content);
} finally {
    if (writer != null) {
        writer.close(); // 可能忘记或失败
    }
}","// 正确：try-with-resources
try (FileWriter writer = new FileWriter(filename)) {
    writer.write(content);
} // 自动关闭
// 正确：多个资源
try (FileInputStream fis = new FileInputStream(source);
     FileOutputStream fos = new FileOutputStream(target)) {
    // 处理逻辑
}"
"Java代码中如何优化性能？","性能问题是P1级别重要问题。优化方法：1. 使用StringBuilder进行字符串拼接；2. 使用Stream API进行集合操作；3. 实施适当的缓存策略；4. 避免不必要的对象创建；5. 选择合适的数据结构。","性能优化","P1","Java","性能优化,StringBuilder,Stream API,缓存,对象创建","// 低效：字符串拼接
String result = """";
for (String item : items) {
    result += item; // 每次创建新String
}
// 低效：不必要的对象创建
List<String> result = new ArrayList<>();
for (Item item : items) {
    result.add(new String(item.getName()));
}","// 高效：StringBuilder
StringBuilder sb = new StringBuilder();
for (String item : items) {
    sb.append(item);
}
String result = sb.toString();
// 高效：Stream API
List<String> result = items.stream()
        .map(Item::getName)
        .collect(Collectors.toList());
// 高效：缓存
@Cacheable(""users"")
public User findById(Long id) {
    return userRepository.findById(id);
}"
"Java代码中应该遵循什么命名规范？","命名规范是P1级别问题，影响代码可读性。Java规范：1. 类名使用大写开头的驼峰命名；2. 方法和变量使用小写开头的驼峰命名；3. 常量使用全大写加下划线；4. 包名使用小写；5. 避免使用缩写。","代码规范","P1","Java","命名规范,驼峰命名,常量命名,包命名","// 违反规范
public class userService { // 类名应大写开头
    private String userName; // 可以但不推荐
    private int maxSize = 100; // 常量应大写
    public String getUserName() { // 方法名冗余
        return userName;
    }
}","// 符合规范
public class UserService {
    private static final int MAX_SIZE = 100;
    private String username;
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
}"
"Java代码中如何正确使用注解？","注解使用不当是P1级别问题。最佳实践：1. 使用构造器注入而非字段注入；2. 明确事务边界和回滚条件；3. 合理使用缓存注解；4. 正确配置验证注解；5. 避免过度使用注解。","代码规范","P1","Java","注解,依赖注入,事务管理,缓存,验证","// 不推荐：字段注入
@Component
public class UserService {
    @Autowired
    private UserRepository userRepository;
    
    @Transactional // 缺少异常配置
    public void updateUser(User user) {
        userRepository.save(user);
    }
}","// 推荐：构造器注入
@Service
@Transactional(readOnly = true)
public class UserService {
    private final UserRepository userRepository;
    
    public UserService(UserRepository userRepository) {
        this.userRepository = userRepository;
    }
    
    @Transactional(rollbackFor = Exception.class)
    public void updateUser(User user) {
        validateUser(user);
        userRepository.save(user);
    }
}"
"Java代码中如何正确使用集合？","集合使用不当是P2级别问题。最佳实践：1. 选择合适的集合类型；2. 使用泛型确保类型安全；3. 考虑线程安全需求；4. 合理设置初始容量；5. 使用不可变集合。","代码规范","P2","Java","集合,泛型,线程安全,初始容量,不可变集合","// 不推荐：使用过时的集合
Vector<User> users = new Vector<>(); // 同步开销
Hashtable<String, User> userMap = new Hashtable<>();
// 不推荐：原始类型
List userList = new ArrayList(); // 缺少泛型
userList.add(""string"");
userList.add(123); // 类型不安全","// 推荐：现代集合类型
List<User> users = new ArrayList<>(100); // 设置初始容量
Map<String, User> userMap = new HashMap<>();
// 推荐：线程安全集合
List<User> concurrentUsers = new CopyOnWriteArrayList<>();
Map<String, User> concurrentMap = new ConcurrentHashMap<>();
// 推荐：不可变集合
List<String> immutableList = List.of(""a"", ""b"", ""c"");
Map<String, Integer> immutableMap = Map.of(""key1"", 1, ""key2"", 2);"
"Java代码中如何正确使用Lambda表达式？","Lambda使用不当是P2级别问题。最佳实践：1. 保持Lambda表达式简洁；2. 使用方法引用提高可读性；3. 避免在Lambda中修改外部变量；4. 合理使用Stream API；5. 注意性能影响。","代码规范","P2","Java","Lambda表达式,方法引用,Stream API,函数式编程","// 不推荐：复杂的Lambda
users.stream().filter(user -> {
    if (user.getAge() > 18) {
        if (user.isActive()) {
            return user.getRole().equals(""ADMIN"");
        }
    }
    return false;
}).collect(Collectors.toList());","// 推荐：简洁的Lambda和方法引用
users.stream()
    .filter(User::isActive)
    .filter(user -> user.getAge() > 18)
    .filter(user -> ""ADMIN"".equals(user.getRole()))
    .collect(Collectors.toList());
// 推荐：提取复杂逻辑到方法
users.stream()
    .filter(this::isEligibleAdmin)
    .collect(Collectors.toList());
private boolean isEligibleAdmin(User user) {
    return user.isActive() && user.getAge() > 18 && ""ADMIN"".equals(user.getRole());
}"
"Java代码中如何正确实现equals和hashCode？","equals和hashCode实现不当是P1级别问题。正确实现：1. 同时重写equals和hashCode；2. 遵循equals契约；3. 使用Objects.equals比较；4. 考虑继承关系；5. 保持一致性。","代码规范","P1","Java","equals,hashCode,对象比较,Objects.equals","// 错误：只重写equals
public class User {
    private String name;
    private String email;
    
    @Override
    public boolean equals(Object obj) {
        if (obj instanceof User) {
            User other = (User) obj;
            return name.equals(other.name); // 可能NPE
        }
        return false;
    }
    // 缺少hashCode重写
}","// 正确：同时重写equals和hashCode
public class User {
    private String name;
    private String email;
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        User user = (User) obj;
        return Objects.equals(name, user.name) && 
               Objects.equals(email, user.email);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(name, email);
    }
}"
"Java代码中如何正确使用日志记录？","日志记录不当是P1级别问题。最佳实践：1. 使用SLF4J门面；2. 选择合适的日志级别；3. 使用参数化消息；4. 避免记录敏感信息；5. 结构化日志记录。","代码规范","P1","Java","日志记录,SLF4J,日志级别,参数化消息,敏感信息","// 不推荐：直接使用具体实现
import java.util.logging.Logger;
Logger logger = Logger.getLogger(UserService.class.getName());
// 不推荐：字符串拼接
logger.info(""User "" + username + "" login with password "" + password);
// 不推荐：不必要的字符串操作
if (logger.isDebugEnabled()) {
    logger.debug(""Processing user: "" + user.toString());
}","// 推荐：使用SLF4J
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
private static final Logger logger = LoggerFactory.getLogger(UserService.class);
// 推荐：参数化消息
logger.info(""User {} attempting login"", username);
// 推荐：结构化日志
logger.info(""User login"", 
    kv(""userId"", user.getId()),
    kv(""action"", ""login""),
    kv(""timestamp"", Instant.now()));
// 推荐：避免敏感信息
logger.info(""User {} login successful"", user.getId()); // 不记录密码"
"Java代码中如何正确处理配置管理？","配置管理不当是P1级别问题。最佳实践：1. 外部化配置；2. 使用类型安全的配置；3. 环境特定配置；4. 配置验证；5. 敏感信息加密。","代码规范","P1","Java","配置管理,外部化配置,类型安全,环境配置,敏感信息","// 不推荐：硬编码配置
@Service
public class EmailService {
    private String smtpHost = ""smtp.gmail.com"";
    private int smtpPort = 587;
    private String username = ""<EMAIL>"";
    private String password = ""password123""; // 硬编码密码
}","// 推荐：外部化配置
@ConfigurationProperties(prefix = ""app.email"")
@Component
@Validated
public class EmailProperties {
    @NotBlank
    private String smtpHost;
    
    @Min(1) @Max(65535)
    private int smtpPort;
    
    @Email
    private String username;
    
    @NotBlank
    private String password;
    
    // getters and setters
}
@Service
public class EmailService {
    private final EmailProperties emailProperties;
    
    public EmailService(EmailProperties emailProperties) {
        this.emailProperties = emailProperties;
    }
}"
