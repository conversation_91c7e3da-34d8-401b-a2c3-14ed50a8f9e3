# Python编码规范Q&A知识库

## Q: Python函数和变量命名应该遵循什么规范？

**A:** 命名规范是P1级别问题，影响代码可读性和团队协作效率。

**问题代码示例：**
```python
# 违反PEP8：使用驼峰命名
def getUserName(userId):
    userName = get_user_by_id(userId)
    return userName

# 违反PEP8：类名使用下划线
class user_model:
    def __init__(self):
        self.maxSize = 100  # 常量应全大写

# 不清晰的命名
def calc(x, y):
    return x * y * 0.1
```

**正确做法：**
```python
# 符合PEP8：函数和变量使用下划线命名
def get_user_name(user_id: int) -> str:
    user_name = get_user_by_id(user_id)
    return user_name

# 符合PEP8：类名使用驼峰命名
class UserModel:
    def __init__(self):
        self.MAX_SIZE = 100  # 常量全大写

# 清晰的命名
def calculate_tax_amount(price: float, tax_rate: float) -> float:
    """计算含税金额"""
    return price * tax_rate * 0.1
```

**命名规范总结：**
- 函数名：`snake_case`（下划线分隔）
- 变量名：`snake_case`
- 类名：`PascalCase`（驼峰命名）
- 常量：`UPPER_SNAKE_CASE`（全大写下划线）
- 私有属性：`_leading_underscore`

**检测关键词：** PEP8, 命名规范, snake_case, PascalCase, 驼峰命名
**风险等级：** P1 - 重要
**修复优先级：** 建议修复

---

## Q: Python代码中如何正确处理异常？

**A:** 异常处理不当是P1级别问题，可能导致程序崩溃或隐藏重要错误。

**问题代码示例：**
```python
# 危险：裸露的except
try:
    risky_operation()
except:  # 捕获所有异常
    pass  # 静默忽略

# 危险：过于宽泛的异常捕获
try:
    process_data()
except Exception as e:
    print("Error occurred")  # 信息不足

# 危险：不当的异常抛出
def validate_input(data):
    if not data:
        raise Exception("Invalid input")  # 应使用具体异常类型
```

**正确做法：**
```python
import logging
from typing import Optional

logger = logging.getLogger(__name__)

# 正确：捕获具体异常类型
def safe_file_operation(filename: str) -> Optional[str]:
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        logger.warning(f"File not found: {filename}")
        return None
    except PermissionError:
        logger.error(f"Permission denied: {filename}")
        raise
    except UnicodeDecodeError as e:
        logger.error(f"Encoding error in {filename}: {e}")
        raise ValueError(f"Cannot decode file {filename}")

# 正确：使用具体异常类型
class ValidationError(ValueError):
    """数据验证错误"""
    pass

def validate_user_input(data: dict) -> dict:
    if not data:
        raise ValidationError("Input data cannot be empty")
    
    if not isinstance(data, dict):
        raise ValidationError("Input must be a dictionary")
    
    required_fields = ['name', 'email']
    for field in required_fields:
        if field not in data:
            raise ValidationError(f"Missing required field: {field}")
    
    return data

# 正确：完整的异常处理链
def process_user_data(raw_data: str) -> dict:
    try:
        # 解析JSON
        import json
        data = json.loads(raw_data)
        
        # 验证数据
        validated_data = validate_user_input(data)
        
        # 处理数据
        result = transform_user_data(validated_data)
        
        logger.info(f"Successfully processed user data for {data.get('name')}")
        return result
        
    except json.JSONDecodeError as e:
        logger.error(f"Invalid JSON data: {e}")
        raise ValidationError("Invalid JSON format")
    except ValidationError:
        # 重新抛出验证错误
        raise
    except Exception as e:
        logger.error(f"Unexpected error processing user data: {e}")
        raise RuntimeError("Failed to process user data")
```

**异常处理最佳实践：**
1. 捕获具体的异常类型，避免裸露的`except:`
2. 记录异常信息到日志系统
3. 使用自定义异常类型表达业务逻辑
4. 在适当的层级处理异常
5. 不要静默忽略异常

**检测关键词：** 异常处理, except, try-catch, 日志记录, 自定义异常
**风险等级：** P1 - 重要
**修复优先级：** 建议修复

---

## Q: Python代码中应该如何添加类型提示？

**A:** 缺少类型提示是P1级别问题，影响代码可读性和IDE支持。

**问题代码示例：**
```python
# 缺少类型提示
def process_user_data(data, options):
    if options.get('validate'):
        return validate_data(data)
    return transform_data(data)

def calculate_total(items):
    total = 0
    for item in items:
        total += item.price
    return total

# 复杂数据结构缺少类型定义
def create_user_profile(user_info):
    return {
        'id': user_info['id'],
        'name': user_info['name'],
        'settings': user_info.get('settings', {})
    }
```

**正确做法：**
```python
from typing import Dict, List, Optional, Any, Union, TypedDict
from dataclasses import dataclass

# 正确：完整的类型提示
def process_user_data(
    data: Dict[str, Any], 
    options: Dict[str, bool]
) -> Optional[Dict[str, Any]]:
    if options.get('validate', False):
        return validate_data(data)
    return transform_data(data)

# 正确：使用dataclass定义数据结构
@dataclass
class Item:
    name: str
    price: float
    quantity: int = 1

def calculate_total(items: List[Item]) -> float:
    total: float = 0.0
    for item in items:
        total += item.price * item.quantity
    return total

# 正确：使用TypedDict定义字典结构
class UserInfo(TypedDict):
    id: int
    name: str
    email: str
    settings: Optional[Dict[str, Any]]

class UserProfile(TypedDict):
    id: int
    name: str
    settings: Dict[str, Any]

def create_user_profile(user_info: UserInfo) -> UserProfile:
    return {
        'id': user_info['id'],
        'name': user_info['name'],
        'settings': user_info.get('settings', {})
    }

# 正确：泛型和联合类型
from typing import Generic, TypeVar

T = TypeVar('T')

class Repository(Generic[T]):
    def __init__(self, model_class: type[T]):
        self.model_class = model_class
    
    def find_by_id(self, id: int) -> Optional[T]:
        # 实现查找逻辑
        pass
    
    def save(self, entity: T) -> T:
        # 实现保存逻辑
        return entity

# 正确：复杂函数签名
def process_batch_data(
    data_list: List[Dict[str, Any]],
    processors: List[callable],
    error_handler: Optional[callable] = None
) -> tuple[List[Dict[str, Any]], List[str]]:
    """
    批量处理数据
    
    Args:
        data_list: 待处理的数据列表
        processors: 处理器函数列表
        error_handler: 错误处理函数
    
    Returns:
        处理成功的数据列表和错误信息列表的元组
    """
    results = []
    errors = []
    
    for data in data_list:
        try:
            for processor in processors:
                data = processor(data)
            results.append(data)
        except Exception as e:
            error_msg = f"Processing failed: {e}"
            if error_handler:
                error_handler(error_msg)
            errors.append(error_msg)
    
    return results, errors
```

**类型提示最佳实践：**
1. 为所有公共函数添加参数和返回值类型提示
2. 使用`TypedDict`定义字典结构
3. 使用`dataclass`定义数据类
4. 使用`Optional`表示可能为None的值
5. 使用`Union`表示多种可能的类型
6. 配合mypy进行静态类型检查

**检测关键词：** 类型提示, typing, TypedDict, dataclass, mypy, 静态类型检查
**风险等级：** P1 - 重要
**修复优先级：** 建议修复

---

## Q: Python代码中如何编写清晰的文档字符串？

**A:** 缺少文档是P1级别问题，影响代码维护和团队协作。

**问题代码示例：**
```python
# 缺少文档
def calculate_discount(price, discount_rate, customer_type):
    if customer_type == 'premium':
        discount_rate *= 1.5
    return price * discount_rate

# 文档不完整
def process_order(order_data):
    """处理订单"""
    # 实现逻辑
    pass
```

**正确做法：**
```python
from typing import List, Dict, Any, Optional
from enum import Enum

class CustomerType(Enum):
    REGULAR = "regular"
    PREMIUM = "premium"
    VIP = "vip"

def calculate_discount(
    price: float, 
    discount_rate: float, 
    customer_type: CustomerType
) -> float:
    """
    计算商品折扣金额。
    
    Args:
        price (float): 商品原价，必须大于0
        discount_rate (float): 基础折扣率，范围0-1
        customer_type (CustomerType): 客户类型，影响最终折扣
    
    Returns:
        float: 折扣金额
    
    Raises:
        ValueError: 当价格小于等于0或折扣率不在有效范围内时
        
    Examples:
        >>> calculate_discount(100.0, 0.1, CustomerType.REGULAR)
        10.0
        >>> calculate_discount(100.0, 0.1, CustomerType.PREMIUM)
        15.0
    
    Note:
        高级客户享受1.5倍折扣率加成
    """
    if price <= 0:
        raise ValueError("Price must be greater than 0")
    if not 0 <= discount_rate <= 1:
        raise ValueError("Discount rate must be between 0 and 1")
    
    # 高级客户享受额外折扣
    if customer_type == CustomerType.PREMIUM:
        discount_rate *= 1.5
    elif customer_type == CustomerType.VIP:
        discount_rate *= 2.0
    
    return price * discount_rate

class OrderProcessor:
    """
    订单处理器，负责订单的验证、处理和状态更新。
    
    Attributes:
        payment_service (PaymentService): 支付服务实例
        inventory_service (InventoryService): 库存服务实例
        notification_service (NotificationService): 通知服务实例
    
    Example:
        >>> processor = OrderProcessor(payment_svc, inventory_svc, notify_svc)
        >>> result = processor.process_order(order_data)
        >>> print(result.status)
        'completed'
    """
    
    def __init__(
        self, 
        payment_service: 'PaymentService',
        inventory_service: 'InventoryService',
        notification_service: 'NotificationService'
    ):
        """
        初始化订单处理器。
        
        Args:
            payment_service: 支付服务实例
            inventory_service: 库存服务实例  
            notification_service: 通知服务实例
        """
        self.payment_service = payment_service
        self.inventory_service = inventory_service
        self.notification_service = notification_service
    
    def process_order(self, order_data: Dict[str, Any]) -> 'OrderResult':
        """
        处理订单的完整流程。
        
        该方法执行以下步骤：
        1. 验证订单数据的完整性和有效性
        2. 检查商品库存是否充足
        3. 处理支付流程
        4. 更新库存数量
        5. 发送确认通知
        
        Args:
            order_data (Dict[str, Any]): 订单数据，包含以下字段：
                - order_id (str): 订单唯一标识
                - user_id (int): 用户ID
                - items (List[Dict]): 商品列表，每个商品包含id、quantity、price
                - payment_method (str): 支付方式
                - shipping_address (Dict): 配送地址信息
        
        Returns:
            OrderResult: 订单处理结果，包含：
                - status (str): 处理状态 ('completed', 'failed', 'pending')
                - order_id (str): 订单ID
                - transaction_id (Optional[str]): 交易ID（成功时）
                - error_message (Optional[str]): 错误信息（失败时）
                - estimated_delivery (Optional[datetime]): 预计送达时间
        
        Raises:
            ValidationError: 订单数据验证失败
            InsufficientStockError: 库存不足
            PaymentError: 支付处理失败
            SystemError: 系统内部错误
        
        Example:
            >>> order_data = {
            ...     'order_id': 'ORD-12345',
            ...     'user_id': 123,
            ...     'items': [{'id': 'ITEM-001', 'quantity': 2, 'price': 29.99}],
            ...     'payment_method': 'credit_card',
            ...     'shipping_address': {'city': 'Beijing', 'district': 'Chaoyang'}
            ... }
            >>> result = processor.process_order(order_data)
            >>> assert result.status == 'completed'
        """
        # 实现订单处理逻辑
        pass
```

**文档字符串最佳实践：**
1. 使用Google风格或NumPy风格的文档格式
2. 包含完整的参数说明和类型信息
3. 说明返回值的结构和含义
4. 列出可能抛出的异常
5. 提供使用示例
6. 对复杂逻辑添加详细说明

**检测关键词：** 文档字符串, docstring, 代码文档, 参数说明, 返回值说明
**风险等级：** P1 - 重要
**修复优先级：** 建议修复

---

## Q: Python代码中如何避免使用魔法数字和字符串？

**A:** 魔法数字和字符串是P1级别问题，降低代码可维护性。

**问题代码示例：**
```python
# 魔法数字
def calculate_tax(amount):
    return amount * 0.08  # 8%税率，但不知道这个数字的含义

def check_user_status(status_code):
    if status_code == 1:  # 不知道1代表什么
        return "Active"
    elif status_code == 2:  # 不知道2代表什么
        return "Inactive"

# 魔法字符串
def send_notification(notification_type):
    if notification_type == "email_welcome":  # 硬编码字符串
        send_email_welcome()
    elif notification_type == "sms_verification":  # 硬编码字符串
        send_sms_verification()
```

**正确做法：**
```python
from enum import Enum
from typing import Final

# 正确：使用常量定义数字
TAX_RATE: Final[float] = 0.08
MAX_RETRY_ATTEMPTS: Final[int] = 3
DEFAULT_TIMEOUT_SECONDS: Final[int] = 30
MAX_FILE_SIZE_MB: Final[int] = 16

def calculate_tax(amount: float) -> float:
    """计算税额，使用当前税率"""
    return amount * TAX_RATE

# 正确：使用枚举定义状态
class UserStatus(Enum):
    ACTIVE = 1
    INACTIVE = 2
    SUSPENDED = 3
    DELETED = 4

def check_user_status(status_code: int) -> str:
    """根据状态码返回用户状态描述"""
    try:
        status = UserStatus(status_code)
        return status.name.title()
    except ValueError:
        return "Unknown"

# 正确：使用枚举定义字符串常量
class NotificationType(Enum):
    EMAIL_WELCOME = "email_welcome"
    EMAIL_PASSWORD_RESET = "email_password_reset"
    SMS_VERIFICATION = "sms_verification"
    PUSH_ORDER_UPDATE = "push_order_update"

def send_notification(notification_type: NotificationType) -> bool:
    """发送指定类型的通知"""
    if notification_type == NotificationType.EMAIL_WELCOME:
        return send_email_welcome()
    elif notification_type == NotificationType.SMS_VERIFICATION:
        return send_sms_verification()
    else:
        raise ValueError(f"Unsupported notification type: {notification_type}")

# 正确：配置类管理常量
class AppConfig:
    """应用配置常量"""
    
    # 数据库配置
    DB_CONNECTION_TIMEOUT: Final[int] = 30
    DB_MAX_CONNECTIONS: Final[int] = 100
    
    # 缓存配置
    CACHE_TTL_SECONDS: Final[int] = 3600
    CACHE_MAX_SIZE: Final[int] = 1000
    
    # 业务规则
    MIN_PASSWORD_LENGTH: Final[int] = 8
    MAX_LOGIN_ATTEMPTS: Final[int] = 5
    SESSION_TIMEOUT_MINUTES: Final[int] = 30
    
    # HTTP状态码
    class HttpStatus:
        OK: Final[int] = 200
        BAD_REQUEST: Final[int] = 400
        UNAUTHORIZED: Final[int] = 401
        NOT_FOUND: Final[int] = 404
        INTERNAL_ERROR: Final[int] = 500

# 使用示例
def validate_password(password: str) -> bool:
    """验证密码强度"""
    return len(password) >= AppConfig.MIN_PASSWORD_LENGTH

def handle_login_attempt(user_id: int, failed_attempts: int) -> bool:
    """处理登录尝试"""
    if failed_attempts >= AppConfig.MAX_LOGIN_ATTEMPTS:
        lock_user_account(user_id)
        return False
    return True
```

**避免魔法数字/字符串的最佳实践：**
1. 使用有意义的常量名替代数字
2. 使用枚举定义相关的常量组
3. 将配置项集中管理
4. 使用`Final`类型提示标记常量
5. 为常量添加注释说明其用途

**检测关键词：** 魔法数字, 魔法字符串, 常量定义, 枚举, Final类型
**风险等级：** P1 - 重要
**修复优先级：** 建议修复
