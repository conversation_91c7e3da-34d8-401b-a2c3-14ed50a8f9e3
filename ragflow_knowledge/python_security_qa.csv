question,answer,category,severity,language,keywords,bad_example,good_example
"如何防止Python代码中的SQL注入攻击？","SQL注入是P0级别的严重安全问题，必须立即修复。主要防护措施：1. 使用参数化查询而非字符串拼接；2. 使用ORM框架；3. 对用户输入进行严格验证；4. 使用白名单验证而非黑名单。","安全编码","P0","Python","SQL注入,字符串拼接,参数化查询,ORM","# 危险：直接字符串拼接
user_id = request.get('user_id')
query = f""SELECT * FROM users WHERE id = {user_id}""
cursor.execute(query)","# 安全：使用参数化查询
cursor.execute(""SELECT * FROM users WHERE id = %s"", (user_id,))
# 安全：使用ORM
User.objects.get(id=user_id)"
"Python代码中如何安全地处理用户密码？","密码处理不当是P0级别安全问题。安全做法：1. 使用bcrypt等强哈希算法；2. 添加随机盐值；3. 绝不明文存储；4. 密码不记录到日志；5. 使用安全的随机数生成器。","安全编码","P0","Python","密码安全,bcrypt,哈希算法,盐值,明文密码","# 危险：明文存储密码
user_password = ""123456""
# 危险：使用弱哈希
password_hash = hashlib.md5(password.encode()).hexdigest()","# 安全：使用bcrypt
import bcrypt
salt = bcrypt.gensalt()
hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
# 安全：验证密码
bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))"
"如何防止Python代码中的命令注入攻击？","命令注入是P0级别严重问题。防护措施：1. 使用subprocess的参数列表形式；2. 避免shell=True；3. 对用户输入进行白名单验证；4. 使用shlex.quote()转义特殊字符；5. 避免使用eval()和exec()。","安全编码","P0","Python","命令注入,subprocess,shell注入,eval,exec","# 危险：直接执行用户输入
import os
os.system(user_input)
# 危险：shell=True
subprocess.call(f""cat {filename}"", shell=True)","# 安全：使用参数列表
subprocess.call([""cat"", filename])
# 安全：白名单验证
ALLOWED_COMMANDS = [""ls"", ""cat"", ""grep""]
if command in ALLOWED_COMMANDS:
    subprocess.call([command] + args)"
"Python代码中如何安全地处理文件上传？","不安全的文件上传可能导致P0级别安全问题。安全措施：1. 验证文件类型和MIME类型；2. 限制文件大小；3. 使用安全的文件名；4. 验证文件内容；5. 存储在安全目录。","安全编码","P0","Python","文件上传,文件验证,MIME类型,文件大小限制","# 危险：不验证直接保存
file = request.files['file']
file.save(f""uploads/{file.filename}"")","# 安全：完整验证
from werkzeug.utils import secure_filename
import magic
filename = secure_filename(file.filename)
# 验证MIME类型
mime = magic.Magic(mime=True)
file_type = mime.from_file(file_path)
if file_type in ALLOWED_MIME_TYPES:
    file.save(safe_path)"
"如何防止Python代码中的XSS攻击？","XSS攻击是P1级别重要问题。防护措施：1. 对所有用户输入进行HTML转义；2. 使用模板引擎的自动转义功能；3. 实施内容安全策略(CSP)；4. 验证和过滤用户输入。","安全编码","P1","Python","XSS,HTML转义,模板注入,CSP","# 危险：直接输出用户数据
return f""<h1>Welcome {username}</h1>""","# 安全：HTML转义
from markupsafe import escape
return f""<h1>Welcome {escape(username)}</h1>""
# 安全：使用模板引擎
return render_template('user.html', username=username)"
"Python代码中如何安全地处理JSON数据？","不安全的JSON处理可能导致P1级别安全问题。安全做法：1. 限制JSON数据大小；2. 验证JSON结构；3. 避免使用pickle反序列化；4. 对解析后的数据进行验证。","安全编码","P1","Python","JSON安全,反序列化,数据验证,pickle","# 危险：反序列化不可信数据
import pickle
user_data = pickle.loads(request.data)
# 危险：不验证JSON结构
data = json.loads(request.data)
username = data['username']  # 可能KeyError","# 安全：验证JSON数据
def safe_json_parse(json_string, max_size=1024*1024):
    if len(json_string) > max_size:
        raise ValueError(""JSON too large"")
    data = json.loads(json_string)
    if not isinstance(data, dict):
        raise ValueError(""Invalid JSON structure"")
    return data"
"如何在Python代码中安全地使用正则表达式？","不安全的正则表达式可能导致P2级别性能问题(ReDoS攻击)。防护措施：1. 避免复杂的嵌套量词；2. 限制输入长度；3. 设置超时保护；4. 使用简单高效的正则模式。","安全编码","P2","Python","正则表达式,ReDoS,性能攻击,超时保护","# 危险：可能导致ReDoS
import re
pattern = r'^(a+)+b$'
re.match(pattern, user_input)","# 安全：简单高效的正则
EMAIL_PATTERN = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
# 安全：添加超时保护
def safe_regex_match(pattern, text, timeout=1):
    if len(text) > 10000:
        return False
    # 添加超时机制
    return bool(pattern.match(text))"
"Python函数和变量命名应该遵循什么规范？","命名规范是P1级别问题，影响代码可读性。PEP8规范：1. 函数和变量使用snake_case；2. 类名使用PascalCase；3. 常量使用UPPER_SNAKE_CASE；4. 私有属性使用_leading_underscore；5. 避免使用保留字。","代码规范","P1","Python","PEP8,命名规范,snake_case,PascalCase","# 违反PEP8：使用驼峰命名
def getUserName(userId):
    userName = get_user_by_id(userId)
    return userName
class user_model:
    def __init__(self):
        self.maxSize = 100","# 符合PEP8：正确命名
def get_user_name(user_id: int) -> str:
    user_name = get_user_by_id(user_id)
    return user_name
class UserModel:
    def __init__(self):
        self.MAX_SIZE = 100"
"Python代码中如何正确处理异常？","异常处理不当是P1级别问题。正确做法：1. 捕获具体异常类型而非Exception；2. 记录异常信息到日志；3. 使用自定义异常表达业务逻辑；4. 不要静默忽略异常；5. 在适当层级处理异常。","代码规范","P1","Python","异常处理,try-catch,日志记录,自定义异常","# 危险：裸露的except
try:
    risky_operation()
except:
    pass  # 静默忽略
# 危险：过于宽泛
except Exception as e:
    print(""Error"")","# 正确：捕获具体异常
try:
    with open(filename, 'r') as f:
        return f.read()
except FileNotFoundError:
    logger.warning(f""File not found: {filename}"")
    return None
except PermissionError:
    logger.error(f""Permission denied: {filename}"")
    raise"
"Python代码中应该如何添加类型提示？","缺少类型提示是P1级别问题，影响代码可读性和IDE支持。最佳实践：1. 为函数参数和返回值添加类型；2. 使用typing模块的类型；3. 使用TypedDict定义字典结构；4. 使用dataclass定义数据类；5. 配合mypy进行检查。","代码规范","P1","Python","类型提示,typing,TypedDict,dataclass,mypy","# 缺少类型提示
def process_data(data, options):
    if options.get('validate'):
        return validate_data(data)
    return transform_data(data)","# 正确：完整类型提示
from typing import Dict, Any, Optional
def process_data(
    data: Dict[str, Any], 
    options: Dict[str, bool]
) -> Optional[Dict[str, Any]]:
    if options.get('validate', False):
        return validate_data(data)
    return transform_data(data)"
"Python代码中如何编写清晰的文档字符串？","缺少文档是P1级别问题，影响代码维护。文档规范：1. 使用Google或NumPy风格；2. 包含完整的参数说明；3. 说明返回值结构；4. 列出可能的异常；5. 提供使用示例；6. 对复杂逻辑详细说明。","代码规范","P1","Python","文档字符串,docstring,参数说明,返回值说明","# 缺少文档
def calculate_discount(price, rate, customer_type):
    if customer_type == 'premium':
        rate *= 1.5
    return price * rate","# 正确：完整文档
def calculate_discount(price: float, rate: float, customer_type: str) -> float:
    \"\"\"计算商品折扣金额。
    
    Args:
        price: 商品原价，必须大于0
        rate: 折扣率，范围0-1
        customer_type: 客户类型
    
    Returns:
        折扣金额
    
    Raises:
        ValueError: 价格或折扣率无效时
    \"\"\""
"Python代码中如何避免使用魔法数字和字符串？","魔法数字和字符串是P1级别问题，降低可维护性。解决方案：1. 使用有意义的常量名；2. 使用枚举定义相关常量组；3. 集中管理配置项；4. 使用Final类型提示；5. 为常量添加注释说明。","代码规范","P1","Python","魔法数字,魔法字符串,常量定义,枚举,Final","# 魔法数字和字符串
def calculate_tax(amount):
    return amount * 0.08  # 不知道0.08是什么
if status_code == 1:  # 不知道1代表什么
    return ""Active""","# 正确：使用常量和枚举
from typing import Final
from enum import Enum
TAX_RATE: Final[float] = 0.08
class UserStatus(Enum):
    ACTIVE = 1
    INACTIVE = 2
def calculate_tax(amount: float) -> float:
    return amount * TAX_RATE"
"如何编写高效的Python循环代码？","低效的循环是P2级别性能问题。优化方法：1. 使用列表推导式替代循环；2. 使用enumerate而非range(len())；3. 使用生成器表达式节省内存；4. 避免在循环中重复计算；5. 使用内置函数如map、filter。","性能优化","P2","Python","循环优化,列表推导式,生成器,enumerate","# 低效：手动索引遍历
items = ['a', 'b', 'c']
for i in range(len(items)):
    print(f""{i}: {items[i]}"")
# 低效：手动构建列表
result = []
for item in items:
    if item.is_valid():
        result.append(item.transform())","# 高效：使用enumerate
for i, item in enumerate(items):
    print(f""{i}: {item}"")
# 高效：列表推导式
result = [item.transform() for item in items if item.is_valid()]
# 高效：生成器表达式
result = (item.transform() for item in items if item.is_valid())"
"Python代码中如何避免内存泄漏？","内存泄漏是P1级别问题，可能导致系统崩溃。防护措施：1. 使用with语句管理资源；2. 及时关闭文件和连接；3. 避免循环引用；4. 使用弱引用；5. 清理事件监听器。","性能优化","P1","Python","内存泄漏,资源管理,with语句,弱引用","# 危险：资源未关闭
f = open('file.txt', 'r')
content = f.read()
return content  # 文件未关闭
# 危险：循环引用
class Parent:
    def __init__(self):
        self.children = []
class Child:
    def __init__(self, parent):
        self.parent = parent
        parent.children.append(self)","# 安全：使用with语句
with open('file.txt', 'r') as f:
    content = f.read()
return content
# 安全：使用弱引用
import weakref
class Parent:
    def __init__(self):
        self._children = weakref.WeakSet()
    def add_child(self, child):
        self._children.add(child)"
"Python代码中如何优化字符串操作？","低效的字符串操作是P2级别性能问题。优化方法：1. 使用join()而非+拼接；2. 使用f-string格式化；3. 避免重复的字符串操作；4. 使用字符串方法而非正则表达式；5. 预编译正则表达式。","性能优化","P2","Python","字符串优化,join,f-string,正则表达式","# 低效：字符串拼接
result = """"
for item in items:
    result += str(item)  # 每次创建新字符串
# 低效：重复操作
for item in items:
    if item.name.lower().strip() == target.lower().strip():
        process(item)","# 高效：使用join
result = ''.join(str(item) for item in items)
# 高效：预处理
target_processed = target.lower().strip()
for item in items:
    if item.name.lower().strip() == target_processed:
        process(item)
# 高效：f-string格式化
message = f""Hello {name}, you have {count} items"""
"如何在Python中正确使用装饰器？","装饰器使用不当是P2级别问题，可能影响性能和调试。最佳实践：1. 使用functools.wraps保持元数据；2. 处理装饰器参数；3. 避免在装饰器中修改全局状态；4. 考虑装饰器的执行顺序；5. 为装饰器添加文档。","代码规范","P2","Python","装饰器,functools.wraps,元数据,装饰器参数","# 问题：不保持函数元数据
def my_decorator(func):
    def wrapper(*args, **kwargs):
        print(""Before call"")
        result = func(*args, **kwargs)
        print(""After call"")
        return result
    return wrapper  # 丢失原函数信息","# 正确：保持函数元数据
import functools
def my_decorator(func):
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        print(""Before call"")
        result = func(*args, **kwargs)
        print(""After call"")
        return result
    return wrapper
# 带参数的装饰器
def retry(max_attempts=3):
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_attempts - 1:
                        raise
            return wrapper
        return decorator"
