rule_id,rule_name,category,severity,description,detection_keywords,impact_score,fix_priority,languages,examples
"CR001","SQL注入检测","安全漏洞","P0","检测代码中可能存在的SQL注入漏洞，包括字符串拼接构建SQL语句的情况","SQL注入,字符串拼接,动态SQL,execute,query",25,"必须修复","Python,Java,C#,PHP","字符串拼接SQL、格式化字符串SQL、未使用参数化查询"
"CR002","命令注入检测","安全漏洞","P0","检测可能导致命令注入的代码模式，如直接执行用户输入的命令","命令注入,os.system,subprocess,shell,exec,eval",25,"必须修复","Python,Java,C#,Node.js","os.system(user_input)、subprocess with shell=True、Runtime.exec"
"CR003","硬编码密码检测","安全漏洞","P0","检测代码中硬编码的密码、API密钥等敏感信息","硬编码,密码,API密钥,secret,token,password",25,"必须修复","所有语言","变量中的明文密码、配置文件中的密钥、代码中的访问令牌"
"CR004","文件路径遍历","安全漏洞","P0","检测可能导致目录遍历攻击的文件操作","路径遍历,../,文件操作,open,read,write",25,"必须修复","所有语言","未验证的文件路径、包含../的路径操作"
"CR005","XSS漏洞检测","安全漏洞","P1","检测可能导致跨站脚本攻击的代码","XSS,HTML输出,模板注入,innerHTML,document.write",15,"建议修复","JavaScript,Python,Java,C#","直接输出用户数据到HTML、未转义的模板变量"
"CR006","CSRF漏洞检测","安全漏洞","P1","检测缺少CSRF保护的表单和API","CSRF,表单提交,POST请求,token验证",15,"建议修复","Web框架","缺少CSRF token的表单、未验证来源的POST请求"
"CR007","异常处理缺失","代码质量","P1","检测缺少异常处理或异常处理不当的代码","异常处理,try-catch,Exception,错误处理",15,"建议修复","所有语言","裸露的except、过于宽泛的异常捕获、静默忽略异常"
"CR008","资源泄漏检测","代码质量","P1","检测可能导致资源泄漏的代码模式","资源泄漏,文件句柄,数据库连接,内存泄漏,close",15,"建议修复","所有语言","文件未关闭、连接未释放、监听器未移除"
"CR009","空指针检测","代码质量","P1","检测可能导致空指针异常的代码","空指针,null,NullPointerException,空值检查",15,"建议修复","Java,C#,JavaScript","未检查null值、链式调用风险、数组访问风险"
"CR010","线程安全问题","代码质量","P1","检测多线程环境下的安全问题","线程安全,竞态条件,同步,锁,volatile",15,"建议修复","Java,C#,Python","共享变量未同步、非线程安全的集合、竞态条件"
"CR011","性能问题检测","性能优化","P1","检测明显的性能问题","性能问题,循环嵌套,字符串拼接,数据库查询",15,"建议修复","所有语言","N+1查询、字符串频繁拼接、嵌套循环、重复计算"
"CR012","命名规范检查","代码规范","P1","检查变量、函数、类的命名是否符合规范","命名规范,驼峰命名,下划线命名,常量命名",15,"建议修复","所有语言","不符合语言规范的命名、使用拼音命名、过短的变量名"
"CR013","代码重复检测","代码质量","P2","检测重复的代码块","代码重复,DRY原则,重构,复制粘贴",10,"建议优化","所有语言","相同的代码块、相似的逻辑实现、重复的验证逻辑"
"CR014","函数长度检查","代码结构","P2","检查函数是否过长","函数长度,方法长度,代码行数,单一职责",10,"建议优化","所有语言","超过50行的函数、职责过多的方法、复杂的逻辑"
"CR015","参数数量检查","代码结构","P2","检查函数参数是否过多","参数数量,函数参数,方法参数,参数对象",10,"建议优化","所有语言","超过5个参数的函数、构造函数参数过多"
"CR016","魔法数字检测","代码可读性","P2","检测代码中的魔法数字和字符串","魔法数字,魔法字符串,常量定义,硬编码",10,"建议优化","所有语言","未定义的数字常量、硬编码的字符串、配置值写死"
"CR017","注释缺失检查","代码文档","P2","检查是否缺少必要的注释和文档","注释,文档,docstring,javadoc,代码说明",10,"建议优化","所有语言","公共方法缺少注释、复杂逻辑无说明、类缺少文档"
"CR018","类型提示缺失","代码质量","P2","检查是否缺少类型提示或类型注解","类型提示,类型注解,typing,泛型",10,"建议优化","Python,TypeScript,C#","函数缺少类型提示、变量类型不明确"
"CR019","未使用变量检测","代码清理","P2","检测定义但未使用的变量","未使用变量,死代码,变量定义,代码清理",10,"建议优化","所有语言","定义但未使用的变量、导入但未使用的模块"
"CR020","调试代码检测","代码清理","P2","检测残留的调试代码","调试代码,console.log,print,debug,测试代码",10,"建议优化","所有语言","console.log语句、print调试信息、临时测试代码"
"CR021","TODO标记检查","代码管理","P3","检查代码中的TODO、FIXME等标记","TODO,FIXME,HACK,临时代码,待办事项",5,"可选修复","所有语言","TODO注释、FIXME标记、临时解决方案"
"CR022","代码格式检查","代码风格","P3","检查代码格式是否符合团队规范","代码格式,缩进,空格,换行,代码风格",5,"可选修复","所有语言","缩进不一致、空格使用不规范、行长度超限"
"CR023","导入顺序检查","代码风格","P3","检查导入语句的顺序是否规范","导入顺序,import,using,模块导入",5,"可选修复","Python,Java,C#","导入顺序混乱、未按字母排序、分组不规范"
"CR024","变量命名长度","代码可读性","P3","检查变量名是否过短或过长","变量命名,命名长度,可读性,变量名",5,"可选修复","所有语言","单字母变量名、过长的变量名、不清晰的缩写"
"CR025","条件表达式复杂度","代码复杂度","P2","检查条件表达式是否过于复杂","条件复杂度,if语句,布尔表达式,逻辑运算",10,"建议优化","所有语言","多重嵌套if、复杂的布尔表达式、过长的条件链"
"CR026","循环复杂度检查","代码复杂度","P2","检查循环嵌套是否过深","循环复杂度,嵌套循环,for循环,while循环",10,"建议优化","所有语言","三层以上嵌套循环、复杂的循环逻辑"
"CR027","类复杂度检查","代码结构","P2","检查类是否过于复杂","类复杂度,类大小,方法数量,职责分离",10,"建议优化","面向对象语言","方法过多的类、职责不清的类、过大的类文件"
"CR028","依赖注入检查","架构设计","P2","检查依赖注入的使用是否合理","依赖注入,IoC,构造器注入,字段注入",10,"建议优化","Java,C#,.NET","字段注入、循环依赖、过度依赖"
"CR029","单元测试覆盖","测试质量","P2","检查是否有足够的单元测试","单元测试,测试覆盖率,test,测试用例",10,"建议优化","所有语言","缺少测试的公共方法、测试覆盖率低、测试用例不充分"
"CR030","API设计检查","接口设计","P2","检查API设计是否合理","API设计,接口设计,RESTful,HTTP方法",10,"建议优化","Web API","不符合REST规范、HTTP方法使用不当、响应格式不统一"
