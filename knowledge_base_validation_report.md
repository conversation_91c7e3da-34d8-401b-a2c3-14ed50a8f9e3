# 知识库验证报告

## 总体统计
- 总文件数: 6
- 有效文件数: 2
- 总错误数: 31
- 总警告数: 59

## knowledge_base/java_cr_knowledge.md
**状态**: ✅ 有效

**警告**:
- ⚠️ 章节'SQL注入漏洞'缺少问题级别标注
- ⚠️ 章节'反序列化漏洞'缺少问题级别标注
- ⚠️ 章节'内存泄漏'缺少问题级别标注
- ⚠️ 章节'线程安全问题'缺少问题级别标注
- ⚠️ 章节'空指针异常'缺少问题级别标注
- ⚠️ 章节'异常处理不当'缺少问题级别标注
- ⚠️ 章节'资源管理不当'缺少问题级别标注
- ⚠️ 章节'性能问题'缺少问题级别标注
- ⚠️ 章节'代码风格问题'缺少问题级别标注
- ⚠️ 章节'设计模式应用'缺少问题级别标注
- ⚠️ 章节'集合使用最佳实践'缺少问题级别标注
- ⚠️ 章节'Lambda表达式和Stream API'缺少问题级别标注
- ⚠️ 章节'注解使用规范'缺少问题级别标注
- ⚠️ 章节'日志记录规范'缺少问题级别标注
- ⚠️ 章节'配置管理'缺少问题级别标注
- ⚠️ 章节'测试编写规范'缺少问题级别标注
- ⚠️ 章节'安全编码实践'缺少问题级别标注

**统计信息**:
- total_lines: 928
- total_chars: 20861
- total_words: 2054
- h1_count: 0
- h2_count: 4
- h3_count: 17
- code_blocks: 26
- bad_case_count: 9
- good_case_count: 9
- p0_problems: 1
- p1_problems: 1
- p2_problems: 1

## knowledge_base/python_cr_knowledge.md
**状态**: ❌ 无效

**错误**:
- ❌ 第43行: 标题层级跳跃过大 (从1级跳到3级)
- ❌ 第88行: 标题层级跳跃过大 (从1级跳到3级)
- ❌ 第128行: 标题层级跳跃过大 (从1级跳到3级)
- ❌ 第174行: 标题层级跳跃过大 (从1级跳到3级)
- ❌ 第279行: 标题层级跳跃过大 (从1级跳到3级)
- ❌ 第336行: 标题层级跳跃过大 (从1级跳到3级)
- ❌ 第436行: 标题层级跳跃过大 (从1级跳到3级)
- ❌ 第486行: 标题层级跳跃过大 (从1级跳到3级)
- ❌ 第578行: 标题层级跳跃过大 (从1级跳到3级)
- ❌ 第607行: 标题层级跳跃过大 (从1级跳到3级)
- ❌ 第651行: 标题层级跳跃过大 (从1级跳到3级)
- ❌ 第681行: 标题层级跳跃过大 (从1级跳到3级)
- ❌ 第726行: 标题层级跳跃过大 (从1级跳到3级)
- ❌ 第826行: 标题层级跳跃过大 (从1级跳到3级)

**警告**:
- ⚠️ 章节'SQL注入漏洞'缺少问题级别标注
- ⚠️ 章节'命令注入漏洞'缺少问题级别标注
- ⚠️ 章节'敏感信息泄露'缺少问题级别标注
- ⚠️ 章节'资源泄漏'缺少问题级别标注
- ⚠️ 章节'线程安全问题'缺少问题级别标注
- ⚠️ 章节'PEP8规范违反'缺少问题级别标注
- ⚠️ 章节'异常处理不当'缺少问题级别标注
- ⚠️ 章节'缺少类型提示'缺少问题级别标注
- ⚠️ 章节'非Pythonic代码风格'缺少问题级别标注
- ⚠️ 章节'代码重复'缺少问题级别标注
- ⚠️ 章节'性能问题'缺少问题级别标注
- ⚠️ 章节'函数设计原则'缺少问题级别标注
- ⚠️ 章节'错误处理最佳实践'缺少问题级别标注
- ⚠️ 章节'日志记录规范'缺少问题级别标注
- ⚠️ 章节'配置管理'缺少问题级别标注
- ⚠️ 章节'测试编写规范'缺少问题级别标注
- ⚠️ 章节'代码文档规范'缺少问题级别标注
- ⚠️ 章节'输入验证'缺少问题级别标注
- ⚠️ 章节'密码处理'缺少问题级别标注

**统计信息**:
- total_lines: 856
- total_chars: 17754
- total_words: 1806
- h1_count: 61
- h2_count: 5
- h3_count: 19
- code_blocks: 30
- bad_case_count: 11
- good_case_count: 11
- p0_problems: 1
- p1_problems: 1
- p2_problems: 1

## knowledge_base/performance_optimization_knowledge.md
**状态**: ❌ 无效

**错误**:
- ❌ 第55行: 标题层级跳跃过大 (从1级跳到4级)
- ❌ 第80行: 标题层级跳跃过大 (从1级跳到3级)
- ❌ 第185行: 标题层级跳跃过大 (从1级跳到4级)
- ❌ 第362行: 标题层级跳跃过大 (从1级跳到4级)

**警告**:
- ⚠️ 章节'时间复杂度优化'缺少问题级别标注
- ⚠️ 章节'空间复杂度优化'缺少问题级别标注
- ⚠️ 章节'查询优化'缺少问题级别标注
- ⚠️ 章节'缓存策略'缺少问题级别标注
- ⚠️ 章节'异步编程'缺少问题级别标注
- ⚠️ 章节'内存管理优化'缺少问题级别标注
- ⚠️ 章节'资源优化'缺少问题级别标注
- ⚠️ 建议添加关键词相关内容: 数据库优化

**统计信息**:
- total_lines: 561
- total_chars: 14593
- total_words: 1277
- h1_count: 15
- h2_count: 4
- h3_count: 7
- code_blocks: 16
- bad_case_count: 0
- good_case_count: 0
- p0_problems: 1
- p1_problems: 6
- p2_problems: 5

## knowledge_base/README.md
**状态**: ❌ 无效

**错误**:
- ❌ 第101行: 标题层级跳跃过大 (从1级跳到3级)
- ❌ 代码块未正确配对 (```数量为奇数)

**警告**:
- ⚠️ 代码块1缺少语言标识
- ⚠️ 代码块2缺少语言标识
- ⚠️ 代码块3缺少语言标识
- ⚠️ 代码块4缺少语言标识
- ⚠️ 代码块5缺少语言标识
- ⚠️ 代码块8缺少语言标识
- ⚠️ 代码块9缺少语言标识

**统计信息**:
- total_lines: 200
- total_chars: 3720
- total_words: 394
- h1_count: 1
- h2_count: 6
- h3_count: 15
- code_blocks: 9
- bad_case_count: 1
- good_case_count: 1
- p0_problems: 7
- p1_problems: 6
- p2_problems: 6

## knowledge_base/general_cr_rules.md
**状态**: ✅ 有效

**统计信息**:
- total_lines: 273
- total_chars: 2669
- total_words: 476
- h1_count: 0
- h2_count: 7
- h3_count: 29
- code_blocks: 0
- bad_case_count: 0
- good_case_count: 0
- p0_problems: 1
- p1_problems: 1
- p2_problems: 1

## knowledge_base/security_coding_knowledge.md
**状态**: ❌ 无效

**错误**:
- ❌ 第36行: 标题层级跳跃过大 (从1级跳到4级)
- ❌ 第68行: 标题层级跳跃过大 (从1级跳到3级)
- ❌ 第117行: 标题层级跳跃过大 (从1级跳到4级)
- ❌ 第152行: 标题层级跳跃过大 (从1级跳到3级)
- ❌ 第188行: 标题层级跳跃过大 (从1级跳到4级)
- ❌ 第207行: 标题层级跳跃过大 (从1级跳到3级)
- ❌ 第249行: 标题层级跳跃过大 (从1级跳到3级)
- ❌ 第277行: 标题层级跳跃过大 (从1级跳到4级)
- ❌ 第303行: 标题层级跳跃过大 (从1级跳到3级)
- ❌ Python代码块9语法错误: 'return' outside function (<code_block_8>, line 5)
- ❌ Python代码块16语法错误: 'return' outside function (<code_block_15>, line 5)

**警告**:
- ⚠️ 章节'注入攻击防护'缺少问题级别标注
- ⚠️ 章节'身份验证和会话管理'缺少问题级别标注
- ⚠️ 章节'跨站脚本攻击(XSS)防护'缺少问题级别标注
- ⚠️ 章节'跨站请求伪造(CSRF)防护'缺少问题级别标注
- ⚠️ 章节'安全配置错误防护'缺少问题级别标注
- ⚠️ 章节'输入验证最佳实践'缺少问题级别标注
- ⚠️ 章节'文件上传安全'缺少问题级别标注
- ⚠️ 章节'API安全最佳实践'缺少问题级别标注

**统计信息**:
- total_lines: 478
- total_chars: 10884
- total_words: 1077
- h1_count: 21
- h2_count: 1
- h3_count: 8
- code_blocks: 21
- bad_case_count: 0
- good_case_count: 0
- p0_problems: 7
- p1_problems: 5
- p2_problems: 1
