# Python代码审查知识库

## P0级别问题 - 严重问题，必须修复

### 1. SQL注入漏洞

**问题描述**：使用字符串拼接构建SQL语句，存在SQL注入风险

**Bad Case**：
```python
# 危险：直接字符串拼接
def get_user(user_id):
    query = f"SELECT * FROM users WHERE id = {user_id}"
    cursor.execute(query)
    
# 危险：格式化字符串
def search_users(name):
    query = "SELECT * FROM users WHERE name = '%s'" % name
    return db.execute(query)
```

**Good Case**：
```python
# 安全：使用参数化查询
def get_user(user_id):
    query = "SELECT * FROM users WHERE id = %s"
    cursor.execute(query, (user_id,))
    
# 安全：使用ORM
def search_users(name):
    return User.objects.filter(name=name)
```

**检测关键词**：SQL注入、字符串拼接、execute、format、%s、f-string with SQL

**修复建议**：
1. 使用参数化查询或预编译语句
2. 使用ORM框架进行数据库操作
3. 对用户输入进行严格验证和转义

---

### 2. 命令注入漏洞

**问题描述**：直接执行用户输入的命令，存在命令注入风险

**Bad Case**：
```python
import os
import subprocess

# 危险：直接执行用户输入
def execute_command(user_input):
    os.system(user_input)
    
# 危险：shell=True with user input
def run_command(filename):
    subprocess.call(f"cat {filename}", shell=True)
```

**Good Case**：
```python
import subprocess
import shlex

# 安全：使用参数列表
def run_command(filename):
    subprocess.call(["cat", filename])
    
# 安全：输入验证和转义
def safe_execute(command, args):
    allowed_commands = ["ls", "cat", "grep"]
    if command not in allowed_commands:
        raise ValueError("Command not allowed")
    subprocess.call([command] + [shlex.quote(arg) for arg in args])
```

**检测关键词**：os.system、subprocess with shell=True、eval、exec、命令注入

**修复建议**：
1. 使用subprocess的参数列表形式
2. 避免shell=True，使用参数数组
3. 对用户输入进行白名单验证
4. 使用shlex.quote()转义特殊字符

---

### 3. 敏感信息泄露

**问题描述**：硬编码密码、API密钥等敏感信息

**Bad Case**：
```python
# 危险：硬编码密码
DATABASE_PASSWORD = "admin123"
API_KEY = "sk-1234567890abcdef"

# 危险：敏感信息在日志中
def login(username, password):
    logger.info(f"User {username} login with password {password}")
```

**Good Case**：
```python
import os
from cryptography.fernet import Fernet

# 安全：使用环境变量
DATABASE_PASSWORD = os.getenv("DB_PASSWORD")
API_KEY = os.getenv("API_KEY")

# 安全：敏感信息脱敏
def login(username, password):
    logger.info(f"User {username} attempting login")
    # 密码不记录到日志
```

**检测关键词**：硬编码、password、api_key、secret、token、明文密码

**修复建议**：
1. 使用环境变量存储敏感信息
2. 使用密钥管理系统（如AWS Secrets Manager）
3. 敏感信息不要记录到日志
4. 对敏感数据进行加密存储

---

### 4. 资源泄漏

**问题描述**：文件、数据库连接等资源未正确释放

**Bad Case**：
```python
# 危险：文件未关闭
def read_file(filename):
    f = open(filename, 'r')
    content = f.read()
    return content  # 文件未关闭

# 危险：数据库连接未关闭
def query_data():
    conn = sqlite3.connect('database.db')
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM users")
    return cursor.fetchall()  # 连接未关闭
```

**Good Case**：
```python
# 安全：使用with语句
def read_file(filename):
    with open(filename, 'r') as f:
        content = f.read()
    return content

# 安全：确保资源释放
def query_data():
    with sqlite3.connect('database.db') as conn:
        cursor = conn.cursor()
        cursor.execute("SELECT * FROM users")
        return cursor.fetchall()
```

**检测关键词**：open without with、connect without close、资源泄漏、文件句柄

**修复建议**：
1. 使用with语句管理资源
2. 在finally块中释放资源
3. 使用上下文管理器
4. 及时关闭不再使用的连接

---

### 5. 线程安全问题

**问题描述**：多线程环境下的竞态条件和数据竞争

**Bad Case**：
```python
import threading

# 危险：全局变量无锁访问
counter = 0

def increment():
    global counter
    counter += 1  # 非原子操作

# 危险：共享资源无同步
shared_dict = {}

def update_dict(key, value):
    shared_dict[key] = value  # 可能导致数据竞争
```

**Good Case**：
```python
import threading
from threading import Lock
from queue import Queue

# 安全：使用锁保护共享资源
counter = 0
counter_lock = Lock()

def increment():
    global counter
    with counter_lock:
        counter += 1

# 安全：使用线程安全的数据结构
from concurrent.futures import ThreadPoolExecutor
import queue

shared_queue = queue.Queue()  # 线程安全的队列
```

**检测关键词**：threading、全局变量、共享资源、锁、同步、竞态条件

**修复建议**：
1. 使用锁（Lock、RLock）保护共享资源
2. 使用线程安全的数据结构
3. 避免全局变量的并发修改
4. 使用原子操作或队列进行线程间通信

---

## P1级别问题 - 重要问题，建议修复

### 1. PEP8规范违反

**问题描述**：违反Python官方代码风格规范

**Bad Case**：
```python
# 违反命名规范
def getUserName(userId):  # 应使用下划线
    userName = get_user_by_id(userId)  # 变量名应使用下划线
    return userName

class user_model:  # 类名应使用驼峰
    def __init__(self):
        self.maxSize = 100  # 常量应全大写

# 违反行长度
very_long_function_call_that_exceeds_the_recommended_line_length_of_79_or_88_characters(param1, param2, param3)
```

**Good Case**：
```python
# 符合PEP8规范
def get_user_name(user_id):
    user_name = get_user_by_id(user_id)
    return user_name

class UserModel:
    def __init__(self):
        self.MAX_SIZE = 100

# 正确的行长度处理
result = very_long_function_call_that_needs_proper_formatting(
    param1,
    param2, 
    param3
)
```

**检测关键词**：命名规范、驼峰命名、下划线、行长度、PEP8

**修复建议**：
1. 函数和变量使用下划线命名法
2. 类名使用驼峰命名法
3. 常量使用全大写加下划线
4. 控制行长度在79-88字符内
5. 使用工具如black、flake8进行格式化

---

### 2. 异常处理不当

**问题描述**：异常处理不规范，影响程序稳定性

**Bad Case**：
```python
# 危险：裸露的except
try:
    risky_operation()
except:  # 捕获所有异常
    pass  # 静默忽略

# 危险：过于宽泛的异常捕获
try:
    process_data()
except Exception as e:  # 太宽泛
    print("Error occurred")  # 信息不足

# 危险：不当的异常抛出
def validate_input(data):
    if not data:
        raise Exception("Invalid input")  # 应使用具体异常类型
```

**Good Case**：
```python
import logging

# 正确：具体的异常处理
try:
    risky_operation()
except ValueError as e:
    logging.error(f"Value error: {e}")
    handle_value_error(e)
except IOError as e:
    logging.error(f"IO error: {e}")
    handle_io_error(e)

# 正确：适当的异常抛出
def validate_input(data):
    if not data:
        raise ValueError("Input data cannot be empty")
    if not isinstance(data, dict):
        raise TypeError("Input data must be a dictionary")
```

**检测关键词**：except、Exception、异常处理、try-catch、raise

**修复建议**：
1. 捕获具体的异常类型
2. 记录异常信息到日志
3. 提供有意义的错误信息
4. 使用具体的异常类型抛出异常
5. 在finally块中清理资源

---

### 3. 缺少类型提示

**问题描述**：缺少类型提示，影响代码可读性和IDE支持

**Bad Case**：
```python
# 缺少类型提示
def process_user_data(data, options):
    if options.get('validate'):
        return validate_data(data)
    return transform_data(data)

def calculate_total(items):
    total = 0
    for item in items:
        total += item.price
    return total
```

**Good Case**：
```python
from typing import Dict, List, Optional, Any, Union

# 添加类型提示
def process_user_data(
    data: Dict[str, Any], 
    options: Dict[str, bool]
) -> Optional[Dict[str, Any]]:
    if options.get('validate'):
        return validate_data(data)
    return transform_data(data)

def calculate_total(items: List['Item']) -> float:
    total: float = 0.0
    for item in items:
        total += item.price
    return total
```

**检测关键词**：类型提示、typing、类型注解、mypy

**修复建议**：
1. 为函数参数和返回值添加类型提示
2. 使用typing模块的类型
3. 为复杂数据结构定义TypedDict
4. 使用mypy进行类型检查

---

## P2级别问题 - 一般问题，建议优化

### 1. 非Pythonic代码风格

**问题描述**：不符合Python习惯用法的代码

**Bad Case**：
```python
# 非Pythonic：手动索引遍历
items = ['a', 'b', 'c']
for i in range(len(items)):
    print(f"{i}: {items[i]}")

# 非Pythonic：手动构建列表
result = []
for item in items:
    if item.is_valid():
        result.append(item.transform())

# 非Pythonic：不使用上下文管理器
f = open('file.txt')
content = f.read()
f.close()
```

**Good Case**：
```python
# Pythonic：使用enumerate
items = ['a', 'b', 'c']
for i, item in enumerate(items):
    print(f"{i}: {item}")

# Pythonic：使用列表推导式
result = [item.transform() for item in items if item.is_valid()]

# Pythonic：使用with语句
with open('file.txt') as f:
    content = f.read()
```

**检测关键词**：Pythonic、列表推导式、enumerate、with语句、生成器

**修复建议**：
1. 使用enumerate而不是range(len())
2. 使用列表推导式和生成器表达式
3. 使用with语句管理资源
4. 利用Python内置函数和标准库
5. 使用解包和多重赋值

---

### 2. 代码重复

**问题描述**：存在重复代码，违反DRY原则

**Bad Case**：
```python
# 重复的验证逻辑
def create_user(name, email):
    if not name or len(name) < 2:
        raise ValueError("Invalid name")
    if not email or '@' not in email:
        raise ValueError("Invalid email")
    # 创建用户逻辑
    
def update_user(user_id, name, email):
    if not name or len(name) < 2:
        raise ValueError("Invalid name")
    if not email or '@' not in email:
        raise ValueError("Invalid email")
    # 更新用户逻辑
```

**Good Case**：
```python
# 提取公共验证逻辑
def validate_user_data(name: str, email: str) -> None:
    if not name or len(name) < 2:
        raise ValueError("Invalid name")
    if not email or '@' not in email:
        raise ValueError("Invalid email")

def create_user(name: str, email: str):
    validate_user_data(name, email)
    # 创建用户逻辑
    
def update_user(user_id: int, name: str, email: str):
    validate_user_data(name, email)
    # 更新用户逻辑
```

**检测关键词**：代码重复、DRY原则、重构、公共方法

**修复建议**：
1. 提取公共方法和函数
2. 使用继承和组合减少重复
3. 创建工具类和辅助函数
4. 使用装饰器处理横切关注点

---

### 3. 性能问题

**问题描述**：存在明显的性能瓶颈

**Bad Case**：
```python
# 性能问题：频繁的字符串拼接
def build_large_string(items):
    result = ""
    for item in items:
        result += str(item)  # 每次都创建新字符串
    return result

# 性能问题：不必要的列表创建
def process_large_data(data):
    return sum([x * 2 for x in data])  # 创建了不必要的列表

# 性能问题：重复计算
def calculate_stats(numbers):
    mean = sum(numbers) / len(numbers)
    variance = sum([(x - sum(numbers) / len(numbers)) ** 2 for x in numbers]) / len(numbers)
    return mean, variance
```

**Good Case**：
```python
# 优化：使用join进行字符串拼接
def build_large_string(items):
    return ''.join(str(item) for item in items)

# 优化：使用生成器表达式
def process_large_data(data):
    return sum(x * 2 for x in data)

# 优化：避免重复计算
def calculate_stats(numbers):
    n = len(numbers)
    total = sum(numbers)
    mean = total / n
    variance = sum((x - mean) ** 2 for x in numbers) / n
    return mean, variance
```

**检测关键词**：性能优化、字符串拼接、生成器、缓存、算法复杂度

**修复建议**：
1. 使用join()进行字符串拼接
2. 使用生成器表达式替代列表推导式
3. 缓存重复计算的结果
4. 选择合适的数据结构和算法
5. 使用内置函数和标准库的优化实现

---

## 最佳实践和开发规则

### 1. 函数设计原则

**单一职责原则**：
```python
# Bad: 函数职责过多
def process_user_data(user_data):
    # 验证数据
    if not user_data.get('email'):
        raise ValueError("Email required")

    # 格式化数据
    user_data['name'] = user_data['name'].strip().title()

    # 保存到数据库
    db.save_user(user_data)

    # 发送邮件
    send_welcome_email(user_data['email'])

# Good: 职责分离
def validate_user_data(user_data):
    if not user_data.get('email'):
        raise ValueError("Email required")

def format_user_data(user_data):
    formatted_data = user_data.copy()
    formatted_data['name'] = user_data['name'].strip().title()
    return formatted_data

def process_user_data(user_data):
    validate_user_data(user_data)
    formatted_data = format_user_data(user_data)
    db.save_user(formatted_data)
    send_welcome_email(formatted_data['email'])
```

### 2. 错误处理最佳实践

**具体异常处理**：
```python
# Bad: 通用异常处理
def read_config_file(filename):
    try:
        with open(filename) as f:
            return json.load(f)
    except Exception as e:
        print(f"Error: {e}")
        return {}

# Good: 具体异常处理
def read_config_file(filename):
    try:
        with open(filename) as f:
            return json.load(f)
    except FileNotFoundError:
        logger.warning(f"Config file {filename} not found, using defaults")
        return {}
    except json.JSONDecodeError as e:
        logger.error(f"Invalid JSON in {filename}: {e}")
        raise ConfigurationError(f"Invalid configuration file: {filename}")
    except PermissionError:
        logger.error(f"Permission denied reading {filename}")
        raise ConfigurationError(f"Cannot read configuration file: {filename}")
```

### 3. 日志记录规范

**结构化日志**：
```python
import logging
import json

# Bad: 非结构化日志
def process_order(order_id, user_id):
    print(f"Processing order {order_id} for user {user_id}")
    # 处理逻辑
    print("Order processed successfully")

# Good: 结构化日志
logger = logging.getLogger(__name__)

def process_order(order_id, user_id):
    logger.info("Processing order", extra={
        'order_id': order_id,
        'user_id': user_id,
        'action': 'order_processing_start'
    })

    try:
        # 处理逻辑
        result = handle_order_processing(order_id)

        logger.info("Order processed successfully", extra={
            'order_id': order_id,
            'user_id': user_id,
            'action': 'order_processing_complete',
            'result': result
        })

    except Exception as e:
        logger.error("Order processing failed", extra={
            'order_id': order_id,
            'user_id': user_id,
            'action': 'order_processing_error',
            'error': str(e)
        })
        raise
```

### 4. 配置管理

**环境配置分离**：
```python
# Bad: 硬编码配置
class DatabaseConfig:
    HOST = "localhost"
    PORT = 5432
    DATABASE = "myapp"
    USERNAME = "admin"
    PASSWORD = "password123"

# Good: 环境变量配置
import os
from dataclasses import dataclass
from typing import Optional

@dataclass
class DatabaseConfig:
    host: str = os.getenv("DB_HOST", "localhost")
    port: int = int(os.getenv("DB_PORT", "5432"))
    database: str = os.getenv("DB_NAME", "myapp")
    username: str = os.getenv("DB_USER", "")
    password: str = os.getenv("DB_PASSWORD", "")

    def __post_init__(self):
        if not self.username or not self.password:
            raise ValueError("Database credentials must be provided via environment variables")
```

### 5. 测试编写规范

**单元测试最佳实践**：
```python
import pytest
from unittest.mock import Mock, patch

# Bad: 测试不够具体
def test_user_service():
    service = UserService()
    result = service.create_user("John", "<EMAIL>")
    assert result is not None

# Good: 具体的测试用例
class TestUserService:
    def setup_method(self):
        self.service = UserService()
        self.valid_user_data = {
            'name': 'John Doe',
            'email': '<EMAIL>',
            'age': 30
        }

    def test_create_user_with_valid_data_returns_user_id(self):
        # Arrange
        expected_user_id = "user_123"

        # Act
        with patch.object(self.service.db, 'save_user', return_value=expected_user_id):
            result = self.service.create_user(self.valid_user_data)

        # Assert
        assert result == expected_user_id
        self.service.db.save_user.assert_called_once_with(self.valid_user_data)

    def test_create_user_with_invalid_email_raises_validation_error(self):
        # Arrange
        invalid_data = self.valid_user_data.copy()
        invalid_data['email'] = 'invalid-email'

        # Act & Assert
        with pytest.raises(ValidationError, match="Invalid email format"):
            self.service.create_user(invalid_data)
```

### 6. 代码文档规范

**Docstring标准**：
```python
# Bad: 缺少文档
def calculate_discount(price, discount_rate, customer_type):
    if customer_type == 'premium':
        discount_rate *= 1.5
    return price * discount_rate

# Good: 完整的文档
def calculate_discount(price: float, discount_rate: float, customer_type: str) -> float:
    """
    计算商品折扣后的价格。

    Args:
        price (float): 商品原价，必须大于0
        discount_rate (float): 折扣率，范围0-1
        customer_type (str): 客户类型，可选值: 'regular', 'premium'

    Returns:
        float: 折扣金额

    Raises:
        ValueError: 当价格小于等于0或折扣率不在有效范围内时

    Examples:
        >>> calculate_discount(100.0, 0.1, 'regular')
        10.0
        >>> calculate_discount(100.0, 0.1, 'premium')
        15.0
    """
    if price <= 0:
        raise ValueError("Price must be greater than 0")
    if not 0 <= discount_rate <= 1:
        raise ValueError("Discount rate must be between 0 and 1")
    if customer_type not in ['regular', 'premium']:
        raise ValueError("Customer type must be 'regular' or 'premium'")

    if customer_type == 'premium':
        discount_rate *= 1.5

    return price * discount_rate
```

---

## 安全编码规范

### 1. 输入验证

**全面的输入验证**：
```python
import re
from typing import Any, Dict

# Bad: 缺少输入验证
def create_user_account(user_data):
    username = user_data['username']
    email = user_data['email']
    password = user_data['password']
    # 直接使用未验证的数据

# Good: 完整的输入验证
def create_user_account(user_data: Dict[str, Any]) -> str:
    """创建用户账户，包含完整的输入验证"""

    # 验证必需字段
    required_fields = ['username', 'email', 'password']
    for field in required_fields:
        if field not in user_data or not user_data[field]:
            raise ValueError(f"Missing required field: {field}")

    username = str(user_data['username']).strip()
    email = str(user_data['email']).strip().lower()
    password = str(user_data['password'])

    # 用户名验证
    if not re.match(r'^[a-zA-Z0-9_]{3,20}$', username):
        raise ValueError("Username must be 3-20 characters, alphanumeric and underscore only")

    # 邮箱验证
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    if not re.match(email_pattern, email):
        raise ValueError("Invalid email format")

    # 密码强度验证
    if len(password) < 8:
        raise ValueError("Password must be at least 8 characters")
    if not re.search(r'[A-Z]', password):
        raise ValueError("Password must contain at least one uppercase letter")
    if not re.search(r'[a-z]', password):
        raise ValueError("Password must contain at least one lowercase letter")
    if not re.search(r'\d', password):
        raise ValueError("Password must contain at least one digit")

    # 创建账户逻辑
    return create_account_internal(username, email, password)
```

### 2. 密码处理

**安全的密码处理**：
```python
import hashlib
import secrets
import bcrypt

# Bad: 不安全的密码处理
def hash_password(password):
    return hashlib.md5(password.encode()).hexdigest()

def verify_password(password, hashed):
    return hashlib.md5(password.encode()).hexdigest() == hashed

# Good: 安全的密码处理
def hash_password(password: str) -> str:
    """使用bcrypt安全地哈希密码"""
    salt = bcrypt.gensalt()
    hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
    return hashed.decode('utf-8')

def verify_password(password: str, hashed: str) -> bool:
    """验证密码"""
    return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))

def generate_secure_token(length: int = 32) -> str:
    """生成安全的随机令牌"""
    return secrets.token_urlsafe(length)
```
