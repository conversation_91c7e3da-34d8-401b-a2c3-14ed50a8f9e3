# 安全编码知识库

## OWASP Top 10 安全风险防护

### 1. 注入攻击防护

#### SQL注入防护
**风险等级**: P0 - 严重

**Python防护**:
```python
# 危险：字符串拼接
query = f"SELECT * FROM users WHERE id = {user_id}"

# 安全：参数化查询
cursor.execute("SELECT * FROM users WHERE id = %s", (user_id,))

# 安全：ORM使用
User.objects.filter(id=user_id)
```

**Java防护**:
```java
// 危险：字符串拼接
String sql = "SELECT * FROM users WHERE id = " + userId;

// 安全：PreparedStatement
PreparedStatement stmt = conn.prepareStatement("SELECT * FROM users WHERE id = ?");
stmt.setString(1, userId);

// 安全：JPA
@Query("SELECT u FROM User u WHERE u.id = :id")
User findById(@Param("id") String id);
```

#### 命令注入防护
**风险等级**: P0 - 严重

**Python防护**:
```python
# 危险：直接执行
os.system(user_input)

# 安全：参数列表
subprocess.run(['ls', '-l', directory])

# 安全：输入验证
allowed_commands = ['ls', 'cat', 'grep']
if command not in allowed_commands:
    raise ValueError("Command not allowed")
```

**Java防护**:
```java
// 危险：Runtime.exec with user input
Runtime.getRuntime().exec(userInput);

// 安全：ProcessBuilder with validation
List<String> allowedCommands = Arrays.asList("ls", "cat", "grep");
if (!allowedCommands.contains(command)) {
    throw new SecurityException("Command not allowed");
}
ProcessBuilder pb = new ProcessBuilder(command, argument);
```

---

### 2. 身份验证和会话管理

#### 密码安全处理
**风险等级**: P0 - 严重

**Python最佳实践**:
```python
import bcrypt
import secrets

# 密码哈希
def hash_password(password: str) -> str:
    salt = bcrypt.gensalt()
    return bcrypt.hashpw(password.encode('utf-8'), salt).decode('utf-8')

# 密码验证
def verify_password(password: str, hashed: str) -> bool:
    return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))

# 安全令牌生成
def generate_secure_token(length: int = 32) -> str:
    return secrets.token_urlsafe(length)
```

**Java最佳实践**:
```java
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import java.security.SecureRandom;

public class PasswordUtils {
    private static final BCryptPasswordEncoder encoder = new BCryptPasswordEncoder();
    
    public static String hashPassword(String password) {
        return encoder.encode(password);
    }
    
    public static boolean verifyPassword(String password, String hash) {
        return encoder.matches(password, hash);
    }
    
    public static String generateSecureToken() {
        SecureRandom random = new SecureRandom();
        byte[] bytes = new byte[32];
        random.nextBytes(bytes);
        return Base64.getEncoder().encodeToString(bytes);
    }
}
```

#### 会话管理
**风险等级**: P1 - 重要

**安全会话配置**:
```python
# Flask会话配置
app.config.update(
    SESSION_COOKIE_SECURE=True,      # 仅HTTPS传输
    SESSION_COOKIE_HTTPONLY=True,    # 防止XSS访问
    SESSION_COOKIE_SAMESITE='Strict', # CSRF防护
    PERMANENT_SESSION_LIFETIME=timedelta(hours=1)  # 会话超时
)
```

```java
// Spring Security会话配置
@Configuration
public class SecurityConfig {
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.sessionManagement()
            .sessionCreationPolicy(SessionCreationPolicy.IF_REQUIRED)
            .maximumSessions(1)
            .maxSessionsPreventsLogin(false)
            .sessionRegistry(sessionRegistry())
            .and()
            .sessionFixation().migrateSession()
            .invalidSessionUrl("/login");
        return http.build();
    }
}
```

---

### 3. 跨站脚本攻击(XSS)防护

#### 输出编码
**风险等级**: P0 - 严重

**Python防护**:
```python
from markupsafe import escape
from html import escape as html_escape

# 危险：直接输出用户数据
return f"<div>Hello {username}</div>"

# 安全：HTML转义
return f"<div>Hello {html_escape(username)}</div>"

# 安全：使用模板引擎自动转义
# Jinja2默认启用自动转义
return render_template('user.html', username=username)
```

**Java防护**:
```java
import org.apache.commons.text.StringEscapeUtils;

// 危险：直接输出
String html = "<div>Hello " + username + "</div>";

// 安全：HTML转义
String html = "<div>Hello " + StringEscapeUtils.escapeHtml4(username) + "</div>";

// 安全：使用模板引擎
// Thymeleaf默认转义
<div th:text="'Hello ' + ${username}"></div>
```

#### 内容安全策略(CSP)
**风险等级**: P1 - 重要

```python
# Flask CSP配置
from flask_talisman import Talisman

csp = {
    'default-src': "'self'",
    'script-src': "'self' 'unsafe-inline'",
    'style-src': "'self' 'unsafe-inline'",
    'img-src': "'self' data: https:",
}

Talisman(app, content_security_policy=csp)
```

---

### 4. 跨站请求伪造(CSRF)防护

#### CSRF令牌
**风险等级**: P1 - 重要

**Python防护**:
```python
from flask_wtf.csrf import CSRFProtect

# 启用CSRF保护
csrf = CSRFProtect(app)
```

**HTML模板**:
```html
<!-- 表单中包含CSRF令牌 -->
<form method="POST">
    {{ csrf_token() }}
    <input type="text" name="data">
    <button type="submit">Submit</button>
</form>
```

**Java防护**:
```java
// Spring Security CSRF配置
@Configuration
public class SecurityConfig {
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http.csrf()
            .csrfTokenRepository(CookieCsrfTokenRepository.withHttpOnlyFalse())
            .and()
            .headers()
            .frameOptions().deny();
        return http.build();
    }
}
```

---

### 5. 安全配置错误防护

#### 敏感信息保护
**风险等级**: P0 - 严重

**环境变量管理**:
```python
import os
from cryptography.fernet import Fernet

# 危险：硬编码敏感信息
API_KEY = "sk-1234567890abcdef"
DATABASE_PASSWORD = "admin123"

# 安全：环境变量
API_KEY = os.getenv("API_KEY")
DATABASE_PASSWORD = os.getenv("DB_PASSWORD")

# 安全：加密存储
def encrypt_sensitive_data(data: str, key: bytes) -> bytes:
    f = Fernet(key)
    return f.encrypt(data.encode())

def decrypt_sensitive_data(encrypted_data: bytes, key: bytes) -> str:
    f = Fernet(key)
    return f.decrypt(encrypted_data).decode()
```

#### 错误信息处理
**风险等级**: P2 - 一般

```python
# 危险：暴露详细错误信息
try:
    process_payment(card_number, amount)
except Exception as e:
    return {"error": str(e)}  # 可能暴露敏感信息

# 安全：通用错误信息
try:
    process_payment(card_number, amount)
except ValidationError as e:
    logger.warning(f"Payment validation failed: {e}")
    return {"error": "Invalid payment information"}
except PaymentError as e:
    logger.error(f"Payment processing failed: {e}")
    return {"error": "Payment processing failed"}
except Exception as e:
    logger.error(f"Unexpected payment error: {e}")
    return {"error": "An unexpected error occurred"}
```

---

### 6. 输入验证最佳实践

#### 白名单验证
**风险等级**: P1 - 重要

**Python验证**:
```python
import re
from typing import List

def validate_username(username: str) -> bool:
    """用户名白名单验证"""
    if not isinstance(username, str):
        return False
    if not 3 <= len(username) <= 20:
        return False
    if not re.match(r'^[a-zA-Z0-9_]+$', username):
        return False
    return True

def validate_email(email: str) -> bool:
    """邮箱格式验证"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))

def validate_file_extension(filename: str, allowed_extensions: List[str]) -> bool:
    """文件扩展名白名单验证"""
    if '.' not in filename:
        return False
    extension = filename.rsplit('.', 1)[1].lower()
    return extension in allowed_extensions
```

**Java验证**:
```java
import javax.validation.constraints.*;
import java.util.regex.Pattern;

public class InputValidator {
    private static final Pattern USERNAME_PATTERN = 
        Pattern.compile("^[a-zA-Z0-9_]{3,20}$");
    private static final Pattern EMAIL_PATTERN = 
        Pattern.compile("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$");
    
    public static boolean validateUsername(String username) {
        return username != null && USERNAME_PATTERN.matcher(username).matches();
    }
    
    public static boolean validateEmail(String email) {
        return email != null && EMAIL_PATTERN.matcher(email).matches();
    }
    
    public static boolean validateFileExtension(String filename, Set<String> allowedExtensions) {
        if (filename == null || !filename.contains(".")) {
            return false;
        }
        String extension = filename.substring(filename.lastIndexOf('.') + 1).toLowerCase();
        return allowedExtensions.contains(extension);
    }
}

// 使用Bean Validation
public class UserRequest {
    @NotBlank(message = "Username cannot be blank")
    @Pattern(regexp = "^[a-zA-Z0-9_]{3,20}$", message = "Invalid username format")
    private String username;
    
    @NotBlank(message = "Email cannot be blank")
    @Email(message = "Invalid email format")
    private String email;
}
```

---

### 7. 文件上传安全

#### 安全文件上传
**风险等级**: P0 - 严重

**Python安全实现**:
```python
import os
import uuid
import magic
from werkzeug.utils import secure_filename

ALLOWED_EXTENSIONS = {'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif'}
MAX_FILE_SIZE = 16 * 1024 * 1024  # 16MB

def allowed_file(filename: str) -> bool:
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def validate_file_content(file_path: str, expected_mime_types: set) -> bool:
    """验证文件实际内容类型"""
    mime = magic.Magic(mime=True)
    file_mime_type = mime.from_file(file_path)
    return file_mime_type in expected_mime_types

def secure_file_upload(file, upload_folder: str) -> str:
    if not file or file.filename == '':
        raise ValueError("No file selected")
    
    if not allowed_file(file.filename):
        raise ValueError("File type not allowed")
    
    # 安全文件名
    filename = secure_filename(file.filename)
    
    # 生成唯一文件名
    unique_filename = f"{uuid.uuid4()}_{filename}"
    file_path = os.path.join(upload_folder, unique_filename)
    
    # 保存文件
    file.save(file_path)
    
    # 验证文件内容
    expected_mime_types = {'image/jpeg', 'image/png', 'application/pdf'}
    if not validate_file_content(file_path, expected_mime_types):
        os.remove(file_path)
        raise ValueError("File content validation failed")
    
    return unique_filename
```

---

### 8. API安全最佳实践

#### 认证和授权
**风险等级**: P0 - 严重

**JWT安全实现**:
```python
import jwt
from datetime import datetime, timedelta

def create_jwt_token(user_id: str, secret_key: str) -> str:
    payload = {
        'user_id': user_id,
        'exp': datetime.utcnow() + timedelta(hours=1),
        'iat': datetime.utcnow(),
        'iss': 'your-app-name'
    }
    return jwt.encode(payload, secret_key, algorithm='HS256')

def verify_jwt_token(token: str, secret_key: str) -> dict:
    try:
        payload = jwt.decode(token, secret_key, algorithms=['HS256'])
        return payload
    except jwt.ExpiredSignatureError:
        raise ValueError("Token has expired")
    except jwt.InvalidTokenError:
        raise ValueError("Invalid token")
```

#### API限流
**风险等级**: P1 - 重要

```python
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address

limiter = Limiter(
    app,
    key_func=get_remote_address,
    default_limits=["200 per day", "50 per hour"]
)

@app.route("/api/sensitive")
@limiter.limit("5 per minute")
def sensitive_endpoint():
    return {"data": "sensitive information"}
```
