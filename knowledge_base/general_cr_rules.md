# 通用代码审查规则和最佳实践

## 问题严重程度分级标准

### P0级别 - 严重问题，必须修复
**影响**：导致系统崩溃、安全漏洞、数据丢失
**示例**：
- 语法错误导致编译失败
- SQL注入、XSS等安全漏洞
- 内存泄漏、资源泄漏
- 死锁、竞态条件
- 业务逻辑错误导致数据不一致

**判断标准**：
- 代码无法正常运行
- 存在明显的安全风险
- 可能导致数据丢失或损坏
- 影响系统稳定性和可用性

---

### P1级别 - 重要问题，建议修复
**影响**：影响代码质量、可维护性、性能
**示例**：
- 违反编码规范
- 异常处理不当
- 缺少必要的文档和注释
- 明显的性能问题
- 缺少单元测试

**判断标准**：
- 违反团队编码标准
- 影响代码可读性和维护性
- 存在潜在的性能问题
- 缺少必要的错误处理

---

### P2级别 - 一般问题，建议优化
**影响**：代码风格、结构优化
**示例**：
- 命名不够清晰
- 代码结构可以优化
- 存在代码重复
- 不符合语言习惯用法
- 调试代码未清理

**判断标准**：
- 不影响功能但可以改进
- 代码风格不够统一
- 存在优化空间
- 不符合最佳实践

---

### P3级别 - 轻微问题，可选修复
**影响**：微小的改进建议
**示例**：
- 变量命名可以更好
- 注释可以更详细
- 代码格式微调

---

## 通用代码审查检查清单

### 1. 功能正确性
- [ ] 代码逻辑是否正确
- [ ] 边界条件是否处理
- [ ] 异常情况是否考虑
- [ ] 业务需求是否满足

### 2. 安全性检查
- [ ] 输入验证是否充分
- [ ] 敏感信息是否保护
- [ ] 权限控制是否正确
- [ ] 是否存在注入漏洞

### 3. 性能考虑
- [ ] 算法复杂度是否合理
- [ ] 是否存在不必要的循环
- [ ] 数据库查询是否优化
- [ ] 内存使用是否合理

### 4. 可维护性
- [ ] 代码结构是否清晰
- [ ] 函数是否职责单一
- [ ] 是否遵循设计原则
- [ ] 代码是否易于理解

### 5. 可测试性
- [ ] 代码是否易于测试
- [ ] 依赖是否可以模拟
- [ ] 是否有足够的单元测试
- [ ] 测试覆盖率是否合理

---

## 常见代码异味识别

### 1. 长方法/长类
**问题**：方法或类过长，难以理解和维护
**识别标准**：
- 方法超过50行
- 类超过500行
- 参数超过5个

**修复建议**：
- 提取方法
- 分解类
- 使用参数对象

### 2. 重复代码
**问题**：相同或相似的代码在多处出现
**识别标准**：
- 相同的代码块出现3次以上
- 相似的逻辑在不同地方实现

**修复建议**：
- 提取公共方法
- 使用模板方法模式
- 创建工具类

### 3. 大量参数
**问题**：方法参数过多，难以使用和理解
**识别标准**：
- 方法参数超过5个
- 构造函数参数过多

**修复建议**：
- 使用参数对象
- 使用建造者模式
- 分解方法职责

### 4. 数据泥团
**问题**：总是一起出现的数据项
**识别标准**：
- 多个方法使用相同的参数组合
- 相同的字段在多个类中出现

**修复建议**：
- 创建数据类
- 封装相关数据
- 使用值对象

### 5. 特性嫉妒
**问题**：一个类过度使用另一个类的方法
**识别标准**：
- 方法中大量调用其他类的方法
- 对其他类的数据过度依赖

**修复建议**：
- 移动方法到合适的类
- 重新分配职责
- 使用委托模式

---

## 代码审查最佳实践

### 1. 审查前准备
- 理解需求和设计
- 了解代码变更范围
- 准备审查检查清单
- 设置合适的审查环境

### 2. 审查过程
- 从整体到细节
- 关注关键路径
- 检查错误处理
- 验证测试覆盖

### 3. 反馈原则
- 具体明确的建议
- 解释问题的影响
- 提供改进方案
- 保持建设性态度

### 4. 常见审查重点

**安全审查重点**：
- 输入验证和过滤
- 权限和访问控制
- 敏感数据处理
- 加密和哈希使用

**性能审查重点**：
- 算法效率
- 数据库查询优化
- 缓存策略
- 资源使用

**可维护性审查重点**：
- 代码结构和组织
- 命名和注释
- 设计模式应用
- 依赖关系

---

## 自动化检查工具建议

### 静态代码分析
- **SonarQube**: 综合代码质量分析
- **ESLint**: JavaScript代码检查
- **Pylint**: Python代码检查
- **Checkstyle**: Java代码风格检查

### 安全扫描
- **OWASP ZAP**: Web应用安全测试
- **Bandit**: Python安全问题检查
- **SpotBugs**: Java安全漏洞检测

### 性能分析
- **JProfiler**: Java性能分析
- **py-spy**: Python性能分析
- **Chrome DevTools**: 前端性能分析

---

## 代码审查流程建议

### 1. 提交前自检
- 运行所有测试
- 执行静态代码分析
- 检查代码格式
- 验证功能完整性

### 2. 同行审查
- 至少一人审查
- 关注业务逻辑
- 检查设计合理性
- 验证测试充分性

### 3. 专家审查
- 架构师审查设计
- 安全专家审查安全
- 性能专家审查性能

### 4. 审查记录
- 记录发现的问题
- 跟踪修复状态
- 总结经验教训
- 持续改进流程

---

## 团队协作规范

### 1. 审查态度
- 对事不对人
- 建设性反馈
- 相互学习
- 持续改进

### 2. 沟通方式
- 清晰表达问题
- 提供具体建议
- 及时响应反馈
- 面对面讨论复杂问题

### 3. 知识分享
- 分享最佳实践
- 讨论设计决策
- 传播新技术
- 建立团队标准

### 4. 持续改进
- 定期回顾审查效果
- 更新审查标准
- 优化审查流程
- 培训团队成员
