# Java代码审查知识库

## P0级别问题 - 严重问题，必须修复

### 1. SQL注入漏洞

**问题描述**：使用字符串拼接构建SQL语句，存在SQL注入风险

**Bad Case**：
```java
// 危险：直接字符串拼接
public User getUserById(String userId) {
    String sql = "SELECT * FROM users WHERE id = " + userId;
    return jdbcTemplate.queryForObject(sql, User.class);
}

// 危险：使用String.format
public List<User> searchUsers(String name) {
    String sql = String.format("SELECT * FROM users WHERE name = '%s'", name);
    return jdbcTemplate.query(sql, new UserRowMapper());
}
```

**Good Case**：
```java
// 安全：使用PreparedStatement
public User getUserById(String userId) {
    String sql = "SELECT * FROM users WHERE id = ?";
    return jdbcTemplate.queryForObject(sql, User.class, userId);
}

// 安全：使用JPA
@Repository
public class UserRepository {
    @Query("SELECT u FROM User u WHERE u.name = :name")
    List<User> findByName(@Param("name") String name);
}
```

**检测关键词**：SQL注入、字符串拼接、String.format、PreparedStatement、参数化查询

**修复建议**：
1. 使用PreparedStatement或参数化查询
2. 使用JPA/Hibernate的参数绑定
3. 对用户输入进行严格验证
4. 使用白名单验证而非黑名单

---

### 2. 反序列化漏洞

**问题描述**：不安全的反序列化操作可能导致远程代码执行

**Bad Case**：
```java
// 危险：直接反序列化不可信数据
public Object deserializeObject(byte[] data) {
    try (ObjectInputStream ois = new ObjectInputStream(new ByteArrayInputStream(data))) {
        return ois.readObject(); // 可能执行恶意代码
    } catch (Exception e) {
        throw new RuntimeException(e);
    }
}

// 危险：使用不安全的JSON库
public User parseUser(String json) {
    // 某些JSON库可能存在反序列化漏洞
    return unsafeJsonLibrary.fromJson(json, User.class);
}
```

**Good Case**：
```java
// 安全：使用白名单验证
public Object deserializeObject(byte[] data, Set<String> allowedClasses) {
    try (ObjectInputStream ois = new SafeObjectInputStream(new ByteArrayInputStream(data), allowedClasses)) {
        return ois.readObject();
    } catch (Exception e) {
        throw new RuntimeException(e);
    }
}

// 安全：使用安全的JSON库
@JsonIgnoreProperties(ignoreUnknown = true)
public class User {
    // 明确定义允许的字段
}

public User parseUser(String json) {
    return objectMapper.readValue(json, User.class);
}
```

**检测关键词**：反序列化、ObjectInputStream、readObject、JSON反序列化

**修复建议**：
1. 避免反序列化不可信数据
2. 使用白名单验证反序列化的类
3. 使用安全的序列化格式（如JSON、XML）
4. 实现自定义的安全反序列化机制

---

### 3. 内存泄漏

**问题描述**：资源未正确释放导致内存泄漏

**Bad Case**：
```java
// 危险：资源未关闭
public String readFile(String filename) {
    FileInputStream fis = new FileInputStream(filename);
    BufferedReader reader = new BufferedReader(new InputStreamReader(fis));
    StringBuilder content = new StringBuilder();
    String line;
    while ((line = reader.readLine()) != null) {
        content.append(line);
    }
    return content.toString(); // 资源未关闭
}

// 危险：监听器未移除
public class EventPublisher {
    private List<EventListener> listeners = new ArrayList<>();
    
    public void addListener(EventListener listener) {
        listeners.add(listener); // 可能导致内存泄漏
    }
}
```

**Good Case**：
```java
// 安全：使用try-with-resources
public String readFile(String filename) throws IOException {
    try (FileInputStream fis = new FileInputStream(filename);
         BufferedReader reader = new BufferedReader(new InputStreamReader(fis))) {
        
        StringBuilder content = new StringBuilder();
        String line;
        while ((line = reader.readLine()) != null) {
            content.append(line);
        }
        return content.toString();
    }
}

// 安全：使用WeakReference
public class EventPublisher {
    private List<WeakReference<EventListener>> listeners = new ArrayList<>();
    
    public void addListener(EventListener listener) {
        listeners.add(new WeakReference<>(listener));
    }
    
    public void removeListener(EventListener listener) {
        listeners.removeIf(ref -> ref.get() == listener || ref.get() == null);
    }
}
```

**检测关键词**：内存泄漏、资源泄漏、try-with-resources、close、WeakReference

**修复建议**：
1. 使用try-with-resources自动管理资源
2. 在finally块中释放资源
3. 使用WeakReference避免强引用
4. 及时移除监听器和回调

---

### 4. 线程安全问题

**问题描述**：多线程环境下的竞态条件和数据竞争

**Bad Case**：
```java
// 危险：非线程安全的单例
public class Singleton {
    private static Singleton instance;
    
    public static Singleton getInstance() {
        if (instance == null) {
            instance = new Singleton(); // 竞态条件
        }
        return instance;
    }
}

// 危险：共享可变状态
public class Counter {
    private int count = 0;
    
    public void increment() {
        count++; // 非原子操作
    }
    
    public int getCount() {
        return count;
    }
}
```

**Good Case**：
```java
// 安全：线程安全的单例
public class Singleton {
    private static volatile Singleton instance;
    
    public static Singleton getInstance() {
        if (instance == null) {
            synchronized (Singleton.class) {
                if (instance == null) {
                    instance = new Singleton();
                }
            }
        }
        return instance;
    }
}

// 安全：使用原子类
public class Counter {
    private final AtomicInteger count = new AtomicInteger(0);
    
    public void increment() {
        count.incrementAndGet();
    }
    
    public int getCount() {
        return count.get();
    }
}
```

**检测关键词**：线程安全、synchronized、volatile、AtomicInteger、ConcurrentHashMap

**修复建议**：
1. 使用synchronized关键字保护共享资源
2. 使用volatile确保变量可见性
3. 使用java.util.concurrent包中的线程安全类
4. 避免共享可变状态

---

### 5. 空指针异常

**问题描述**：未进行空值检查导致NullPointerException

**Bad Case**：
```java
// 危险：未检查空值
public String processUser(User user) {
    return user.getName().toUpperCase(); // 可能抛出NPE
}

// 危险：链式调用未检查
public String getUserEmail(Long userId) {
    User user = userService.findById(userId);
    return user.getProfile().getEmail().toLowerCase(); // 多个NPE风险点
}
```

**Good Case**：
```java
// 安全：空值检查
public String processUser(User user) {
    if (user == null || user.getName() == null) {
        throw new IllegalArgumentException("User and user name cannot be null");
    }
    return user.getName().toUpperCase();
}

// 安全：使用Optional
public Optional<String> getUserEmail(Long userId) {
    return Optional.ofNullable(userService.findById(userId))
            .map(User::getProfile)
            .map(Profile::getEmail)
            .map(String::toLowerCase);
}

// 安全：使用注解
public String processUser(@NonNull User user) {
    Objects.requireNonNull(user.getName(), "User name cannot be null");
    return user.getName().toUpperCase();
}
```

**检测关键词**：NullPointerException、空指针、Optional、@NonNull、Objects.requireNonNull

**修复建议**：
1. 进行显式的空值检查
2. 使用Optional处理可能为空的值
3. 使用@NonNull等注解标记
4. 使用Objects.requireNonNull进行参数验证

---

## P1级别问题 - 重要问题，建议修复

### 1. 异常处理不当

**问题描述**：异常处理不规范，影响程序稳定性

**Bad Case**：
```java
// 危险：捕获过于宽泛的异常
public void processData() {
    try {
        // 业务逻辑
        complexOperation();
    } catch (Exception e) { // 过于宽泛
        e.printStackTrace(); // 不应该直接打印
    }
}

// 危险：静默忽略异常
public String readConfig() {
    try {
        return Files.readString(Paths.get("config.txt"));
    } catch (IOException e) {
        return ""; // 静默忽略，可能隐藏问题
    }
}
```

**Good Case**：
```java
// 正确：具体的异常处理
public void processData() throws ProcessingException {
    try {
        complexOperation();
    } catch (ValidationException e) {
        logger.error("Data validation failed", e);
        throw new ProcessingException("Invalid data", e);
    } catch (IOException e) {
        logger.error("IO error during processing", e);
        throw new ProcessingException("IO error", e);
    }
}

// 正确：适当的异常处理和日志记录
public String readConfig() throws ConfigurationException {
    try {
        return Files.readString(Paths.get("config.txt"));
    } catch (IOException e) {
        logger.error("Failed to read configuration file", e);
        throw new ConfigurationException("Configuration file not accessible", e);
    }
}
```

**检测关键词**：异常处理、Exception、try-catch、printStackTrace、日志记录

**修复建议**：
1. 捕获具体的异常类型
2. 记录异常信息到日志系统
3. 提供有意义的错误信息
4. 避免静默忽略异常
5. 使用自定义异常类型

---

### 2. 资源管理不当

**问题描述**：资源未正确管理，可能导致资源泄漏

**Bad Case**：
```java
// 危险：手动管理资源
public void writeToFile(String filename, String content) {
    FileWriter writer = null;
    try {
        writer = new FileWriter(filename);
        writer.write(content);
    } catch (IOException e) {
        e.printStackTrace();
    } finally {
        if (writer != null) {
            try {
                writer.close(); // 可能忘记关闭或关闭失败
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
}
```

**Good Case**：
```java
// 正确：使用try-with-resources
public void writeToFile(String filename, String content) throws IOException {
    try (FileWriter writer = new FileWriter(filename)) {
        writer.write(content);
    } // 自动关闭资源
}

// 正确：多个资源的管理
public void copyFile(String source, String target) throws IOException {
    try (FileInputStream fis = new FileInputStream(source);
         FileOutputStream fos = new FileOutputStream(target)) {
        
        byte[] buffer = new byte[1024];
        int length;
        while ((length = fis.read(buffer)) > 0) {
            fos.write(buffer, 0, length);
        }
    }
}
```

**检测关键词**：资源管理、try-with-resources、AutoCloseable、finally、close

**修复建议**：
1. 使用try-with-resources自动管理资源
2. 实现AutoCloseable接口
3. 在finally块中确保资源释放
4. 使用资源池管理昂贵资源

---

### 3. 性能问题

**问题描述**：存在明显的性能瓶颈

**Bad Case**：
```java
// 性能问题：字符串拼接
public String buildLargeString(List<String> items) {
    String result = "";
    for (String item : items) {
        result += item; // 每次都创建新的String对象
    }
    return result;
}

// 性能问题：不必要的对象创建
public List<String> processItems(List<Item> items) {
    List<String> result = new ArrayList<>();
    for (Item item : items) {
        result.add(new String(item.getName())); // 不必要的String创建
    }
    return result;
}
```

**Good Case**：
```java
// 优化：使用StringBuilder
public String buildLargeString(List<String> items) {
    StringBuilder sb = new StringBuilder();
    for (String item : items) {
        sb.append(item);
    }
    return sb.toString();
}

// 优化：避免不必要的对象创建
public List<String> processItems(List<Item> items) {
    return items.stream()
            .map(Item::getName)
            .collect(Collectors.toList());
}

// 优化：使用缓存
@Service
public class UserService {
    @Cacheable("users")
    public User findById(Long id) {
        return userRepository.findById(id);
    }
}
```

**检测关键词**：性能优化、StringBuilder、Stream API、缓存、对象创建

**修复建议**：
1. 使用StringBuilder进行字符串拼接
2. 使用Stream API进行集合操作
3. 实现适当的缓存策略
4. 避免不必要的对象创建
5. 选择合适的数据结构

---

## P2级别问题 - 一般问题，建议优化

### 1. 代码风格问题

**问题描述**：不符合Java编码规范的代码风格

**Bad Case**：
```java
// 违反命名规范
public class userService { // 类名应该大写开头
    private String userName; // 可以，但不如使用完整单词
    private int maxSize = 100; // 常量应该大写
    
    public String getUserName() { // 方法名可以更简洁
        return userName;
    }
    
    public void setUserName(String userName) {
        this.userName = userName;
    }
}
```

**Good Case**：
```java
// 符合Java规范
public class UserService {
    private static final int MAX_SIZE = 100;
    private String username;
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
}
```

**检测关键词**：命名规范、代码风格、常量、方法命名

**修复建议**：
1. 类名使用大写开头的驼峰命名
2. 方法和变量使用小写开头的驼峰命名
3. 常量使用全大写加下划线
4. 遵循Java代码规范（如Google Java Style Guide）

---

### 2. 设计模式应用

**单例模式最佳实践**：
```java
// Bad: 非线程安全的单例
public class DatabaseConnection {
    private static DatabaseConnection instance;

    public static DatabaseConnection getInstance() {
        if (instance == null) {
            instance = new DatabaseConnection();
        }
        return instance;
    }
}

// Good: 枚举单例（推荐）
public enum DatabaseConnection {
    INSTANCE;

    private Connection connection;

    DatabaseConnection() {
        // 初始化连接
        this.connection = createConnection();
    }

    public Connection getConnection() {
        return connection;
    }
}

// Good: 双重检查锁定
public class ConfigManager {
    private static volatile ConfigManager instance;

    public static ConfigManager getInstance() {
        if (instance == null) {
            synchronized (ConfigManager.class) {
                if (instance == null) {
                    instance = new ConfigManager();
                }
            }
        }
        return instance;
    }
}
```

### 3. 集合使用最佳实践

**选择合适的集合类型**：
```java
// Bad: 使用不合适的集合类型
public class UserManager {
    private Vector<User> users = new Vector<>(); // Vector是同步的，通常不需要
    private Hashtable<String, User> userMap = new Hashtable<>(); // 同样过时

    public void addUser(User user) {
        users.add(user);
        userMap.put(user.getId(), user);
    }
}

// Good: 使用现代集合类型
public class UserManager {
    private final List<User> users = new ArrayList<>();
    private final Map<String, User> userMap = new HashMap<>();

    public synchronized void addUser(User user) {
        users.add(user);
        userMap.put(user.getId(), user);
    }

    // 或者使用并发集合
    private final List<User> concurrentUsers = new CopyOnWriteArrayList<>();
    private final Map<String, User> concurrentUserMap = new ConcurrentHashMap<>();
}
```

### 4. Lambda表达式和Stream API

**现代Java特性的使用**：
```java
// Bad: 传统的循环和匿名类
public List<String> getActiveUserNames(List<User> users) {
    List<String> result = new ArrayList<>();
    for (User user : users) {
        if (user.isActive()) {
            result.add(user.getName());
        }
    }
    Collections.sort(result, new Comparator<String>() {
        @Override
        public int compare(String s1, String s2) {
            return s1.compareToIgnoreCase(s2);
        }
    });
    return result;
}

// Good: 使用Stream API和Lambda
public List<String> getActiveUserNames(List<User> users) {
    return users.stream()
            .filter(User::isActive)
            .map(User::getName)
            .sorted(String::compareToIgnoreCase)
            .collect(Collectors.toList());
}

// Good: 复杂的Stream操作
public Map<String, List<User>> groupUsersByDepartment(List<User> users) {
    return users.stream()
            .filter(user -> user.getDepartment() != null)
            .collect(Collectors.groupingBy(User::getDepartment));
}
```

### 5. 注解使用规范

**Spring框架注解最佳实践**：
```java
// Bad: 注解使用不当
@Component
public class UserService {
    @Autowired
    private UserRepository userRepository; // 字段注入不推荐

    @Transactional // 缺少异常处理配置
    public void updateUser(User user) {
        userRepository.save(user);
    }
}

// Good: 正确的注解使用
@Service
@Transactional(readOnly = true) // 类级别的只读事务
public class UserService {

    private final UserRepository userRepository;

    // 构造器注入（推荐）
    public UserService(UserRepository userRepository) {
        this.userRepository = userRepository;
    }

    @Transactional(rollbackFor = Exception.class) // 明确回滚条件
    public void updateUser(User user) {
        validateUser(user);
        userRepository.save(user);
    }

    public Optional<User> findById(Long id) {
        return userRepository.findById(id);
    }
}
```

---

## 最佳实践和开发规则

### 1. 日志记录规范

**结构化日志记录**：
```java
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;

// Bad: 不规范的日志记录
public class OrderService {
    public void processOrder(Order order) {
        System.out.println("Processing order: " + order.getId());
        try {
            // 处理逻辑
            System.out.println("Order processed successfully");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}

// Good: 规范的日志记录
@Service
public class OrderService {
    private static final Logger logger = LoggerFactory.getLogger(OrderService.class);

    public void processOrder(Order order) {
        MDC.put("orderId", order.getId());
        MDC.put("userId", order.getUserId());

        logger.info("Starting order processing for order: {}", order.getId());

        try {
            validateOrder(order);
            processPayment(order);
            updateInventory(order);

            logger.info("Order processed successfully: {}", order.getId());

        } catch (ValidationException e) {
            logger.warn("Order validation failed: {}", e.getMessage());
            throw e;
        } catch (PaymentException e) {
            logger.error("Payment processing failed for order: {}", order.getId(), e);
            throw e;
        } catch (Exception e) {
            logger.error("Unexpected error processing order: {}", order.getId(), e);
            throw new OrderProcessingException("Failed to process order", e);
        } finally {
            MDC.clear();
        }
    }
}
```

### 2. 配置管理

**Spring Boot配置最佳实践**：
```java
// Bad: 硬编码配置
@Service
public class EmailService {
    private String smtpHost = "smtp.gmail.com";
    private int smtpPort = 587;
    private String username = "<EMAIL>";
    private String password = "password123";
}

// Good: 外部化配置
@ConfigurationProperties(prefix = "app.email")
@Component
@Validated
public class EmailProperties {

    @NotBlank
    private String smtpHost;

    @Min(1)
    @Max(65535)
    private int smtpPort;

    @Email
    private String username;

    @NotBlank
    private String password;

    // getters and setters
}

@Service
public class EmailService {
    private final EmailProperties emailProperties;

    public EmailService(EmailProperties emailProperties) {
        this.emailProperties = emailProperties;
    }

    public void sendEmail(String to, String subject, String body) {
        // 使用配置属性
        String host = emailProperties.getSmtpHost();
        int port = emailProperties.getSmtpPort();
        // 发送邮件逻辑
    }
}
```

### 3. 测试编写规范

**单元测试最佳实践**：
```java
// Bad: 测试不够具体
@Test
public void testUserService() {
    UserService service = new UserService();
    User result = service.createUser("John", "<EMAIL>");
    assertNotNull(result);
}

// Good: 具体的测试用例
@ExtendWith(MockitoExtension.class)
class UserServiceTest {

    @Mock
    private UserRepository userRepository;

    @Mock
    private EmailService emailService;

    @InjectMocks
    private UserService userService;

    @Test
    @DisplayName("Should create user successfully with valid data")
    void shouldCreateUserSuccessfullyWithValidData() {
        // Given
        String name = "John Doe";
        String email = "<EMAIL>";
        User expectedUser = new User(name, email);

        when(userRepository.existsByEmail(email)).thenReturn(false);
        when(userRepository.save(any(User.class))).thenReturn(expectedUser);

        // When
        User actualUser = userService.createUser(name, email);

        // Then
        assertThat(actualUser).isNotNull();
        assertThat(actualUser.getName()).isEqualTo(name);
        assertThat(actualUser.getEmail()).isEqualTo(email);

        verify(userRepository).existsByEmail(email);
        verify(userRepository).save(any(User.class));
        verify(emailService).sendWelcomeEmail(email);
    }

    @Test
    @DisplayName("Should throw exception when email already exists")
    void shouldThrowExceptionWhenEmailAlreadyExists() {
        // Given
        String email = "<EMAIL>";
        when(userRepository.existsByEmail(email)).thenReturn(true);

        // When & Then
        assertThatThrownBy(() -> userService.createUser("John", email))
                .isInstanceOf(UserAlreadyExistsException.class)
                .hasMessage("User with email already exists: " + email);

        verify(userRepository).existsByEmail(email);
        verify(userRepository, never()).save(any(User.class));
    }
}
```

### 4. 安全编码实践

**输入验证和安全处理**：
```java
// Bad: 缺少输入验证
@RestController
public class UserController {

    @PostMapping("/users")
    public User createUser(@RequestBody CreateUserRequest request) {
        return userService.createUser(request.getName(), request.getEmail());
    }
}

// Good: 完整的输入验证
@RestController
@Validated
public class UserController {

    private final UserService userService;

    public UserController(UserService userService) {
        this.userService = userService;
    }

    @PostMapping("/users")
    public ResponseEntity<User> createUser(
            @Valid @RequestBody CreateUserRequest request) {

        try {
            User user = userService.createUser(request.getName(), request.getEmail());
            return ResponseEntity.status(HttpStatus.CREATED).body(user);

        } catch (ValidationException e) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST, e.getMessage());
        } catch (UserAlreadyExistsException e) {
            throw new ResponseStatusException(HttpStatus.CONFLICT, e.getMessage());
        }
    }
}

// 请求DTO验证
public class CreateUserRequest {

    @NotBlank(message = "Name cannot be blank")
    @Size(min = 2, max = 50, message = "Name must be between 2 and 50 characters")
    private String name;

    @NotBlank(message = "Email cannot be blank")
    @Email(message = "Email must be valid")
    private String email;

    // getters and setters
}
```
