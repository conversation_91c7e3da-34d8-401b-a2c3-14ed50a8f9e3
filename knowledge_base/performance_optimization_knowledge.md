# 性能优化知识库

## 算法复杂度优化

### 1. 时间复杂度优化

#### 避免嵌套循环
**问题等级**: P2 - 一般

**Python优化**:
```python
# 低效：O(n²) 嵌套循环
def find_duplicates_slow(arr1, arr2):
    duplicates = []
    for item1 in arr1:
        for item2 in arr2:
            if item1 == item2:
                duplicates.append(item1)
    return duplicates

# 高效：O(n) 使用集合
def find_duplicates_fast(arr1, arr2):
    set2 = set(arr2)
    return [item for item in arr1 if item in set2]

# 更高效：直接集合运算
def find_duplicates_optimal(arr1, arr2):
    return list(set(arr1) & set(arr2))
```

**Java优化**:
```java
// 低效：O(n²) 嵌套循环
public List<String> findDuplicatesSlow(List<String> list1, List<String> list2) {
    List<String> duplicates = new ArrayList<>();
    for (String item1 : list1) {
        for (String item2 : list2) {
            if (item1.equals(item2)) {
                duplicates.add(item1);
            }
        }
    }
    return duplicates;
}

// 高效：O(n) 使用HashSet
public List<String> findDuplicatesFast(List<String> list1, List<String> list2) {
    Set<String> set2 = new HashSet<>(list2);
    return list1.stream()
            .filter(set2::contains)
            .collect(Collectors.toList());
}
```

#### 选择合适的数据结构
**问题等级**: P1 - 重要

**Python数据结构选择**:
```python
# 低效：频繁查找使用列表
def process_user_data_slow(users, target_ids):
    target_users = []
    for user in users:
        if user.id in target_ids:  # O(n) 查找
            target_users.append(user)
    return target_users

# 高效：使用集合进行查找
def process_user_data_fast(users, target_ids):
    target_id_set = set(target_ids)  # O(1) 查找
    return [user for user in users if user.id in target_id_set]

# 字典用于快速映射
def create_user_lookup(users):
    return {user.id: user for user in users}  # O(1) 访问
```

---

### 2. 空间复杂度优化

#### 生成器vs列表
**问题等级**: P2 - 一般

**Python内存优化**:
```python
# 内存密集：创建大列表
def process_large_data_memory_intensive(data):
    processed = [expensive_operation(item) for item in data]
    return sum(processed)

# 内存友好：使用生成器
def process_large_data_memory_friendly(data):
    processed = (expensive_operation(item) for item in data)
    return sum(processed)

# 流式处理大文件
def process_large_file_streaming(filename):
    with open(filename, 'r') as file:
        for line in file:  # 逐行读取，不加载整个文件
            yield process_line(line)
```

**Java流式处理**:
```java
// 内存密集：加载所有数据
public List<ProcessedData> processAllData(List<RawData> data) {
    return data.stream()
            .map(this::expensiveOperation)
            .collect(Collectors.toList());  // 存储所有结果
}

// 内存友好：流式处理
public long processDataStreaming(List<RawData> data) {
    return data.stream()
            .mapToLong(this::expensiveOperation)
            .sum();  // 不存储中间结果
}
```

---

## 数据库性能优化

### 1. 查询优化

#### 避免N+1查询问题
**问题等级**: P1 - 重要

**Python ORM优化**:
```python
# 低效：N+1查询
def get_users_with_posts_slow():
    users = User.objects.all()
    result = []
    for user in users:  # 1次查询
        posts = user.posts.all()  # N次查询
        result.append({
            'user': user,
            'post_count': len(posts)
        })
    return result

# 高效：预加载关联数据
def get_users_with_posts_fast():
    users = User.objects.prefetch_related('posts').all()  # 2次查询
    return [{
        'user': user,
        'post_count': user.posts.count()
    } for user in users]

# 更高效：聚合查询
def get_users_with_post_counts():
    return User.objects.annotate(
        post_count=Count('posts')
    ).values('id', 'name', 'post_count')  # 1次查询
```

**Java JPA优化**:
```java
// 低效：N+1查询
@Service
public class UserService {
    public List<UserWithPostCount> getUsersWithPostsSlow() {
        List<User> users = userRepository.findAll();  // 1次查询
        return users.stream()
                .map(user -> {
                    int postCount = user.getPosts().size();  // N次查询
                    return new UserWithPostCount(user, postCount);
                })
                .collect(Collectors.toList());
    }
    
    // 高效：使用JOIN FETCH
    @Query("SELECT u FROM User u LEFT JOIN FETCH u.posts")
    List<User> findAllWithPosts();  // 1次查询
    
    // 更高效：聚合查询
    @Query("SELECT new com.example.UserWithPostCount(u, COUNT(p)) " +
           "FROM User u LEFT JOIN u.posts p GROUP BY u")
    List<UserWithPostCount> findUsersWithPostCounts();
}
```

#### 索引优化
**问题等级**: P1 - 重要

**数据库索引策略**:
```sql
-- 低效：全表扫描
SELECT * FROM users WHERE email = '<EMAIL>';

-- 高效：在email字段创建索引
CREATE INDEX idx_users_email ON users(email);

-- 复合索引优化
CREATE INDEX idx_users_status_created ON users(status, created_at);

-- 查询优化
SELECT id, name FROM users 
WHERE status = 'active' 
  AND created_at > '2024-01-01'
ORDER BY created_at DESC;
```

---

### 2. 缓存策略

#### 多级缓存
**问题等级**: P1 - 重要

**Python缓存实现**:
```python
import redis
from functools import wraps
from typing import Optional

class CacheManager:
    def __init__(self):
        self.redis_client = redis.Redis(host='localhost', port=6379, db=0)
        self.local_cache = {}  # 本地缓存
        
    def get_from_cache(self, key: str) -> Optional[str]:
        # 1. 检查本地缓存
        if key in self.local_cache:
            return self.local_cache[key]
            
        # 2. 检查Redis缓存
        value = self.redis_client.get(key)
        if value:
            # 回填本地缓存
            self.local_cache[key] = value.decode('utf-8')
            return value.decode('utf-8')
            
        return None
    
    def set_cache(self, key: str, value: str, ttl: int = 3600):
        # 设置Redis缓存
        self.redis_client.setex(key, ttl, value)
        # 设置本地缓存
        self.local_cache[key] = value

def cache_result(ttl: int = 3600):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            cache_key = f"{func.__name__}:{hash(str(args) + str(kwargs))}"
            
            # 尝试从缓存获取
            cached_result = cache_manager.get_from_cache(cache_key)
            if cached_result:
                return json.loads(cached_result)
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            cache_manager.set_cache(cache_key, json.dumps(result), ttl)
            return result
        return wrapper
    return decorator

@cache_result(ttl=1800)
def get_user_profile(user_id: int):
    # 昂贵的数据库查询
    return database.query_user_profile(user_id)
```

**Java Spring缓存**:
```java
@Service
public class UserService {
    
    @Cacheable(value = "userProfiles", key = "#userId")
    public UserProfile getUserProfile(Long userId) {
        // 昂贵的数据库查询
        return userRepository.findProfileById(userId);
    }
    
    @CacheEvict(value = "userProfiles", key = "#userId")
    public void updateUserProfile(Long userId, UserProfile profile) {
        userRepository.updateProfile(userId, profile);
    }
    
    @Cacheable(value = "userStats", key = "#userId", unless = "#result.isEmpty()")
    public List<UserStats> getUserStats(Long userId) {
        return statsRepository.findByUserId(userId);
    }
}

@Configuration
@EnableCaching
public class CacheConfig {
    
    @Bean
    public CacheManager cacheManager() {
        RedisCacheManager.Builder builder = RedisCacheManager
                .RedisCacheManagerBuilder
                .fromConnectionFactory(redisConnectionFactory())
                .cacheDefaults(cacheConfiguration());
        return builder.build();
    }
    
    private RedisCacheConfiguration cacheConfiguration() {
        return RedisCacheConfiguration.defaultCacheConfig()
                .entryTtl(Duration.ofMinutes(30))
                .serializeKeysWith(RedisSerializationContext.SerializationPair
                        .fromSerializer(new StringRedisSerializer()))
                .serializeValuesWith(RedisSerializationContext.SerializationPair
                        .fromSerializer(new GenericJackson2JsonRedisSerializer()));
    }
}
```

---

## 并发性能优化

### 1. 异步编程

#### Python异步优化
**问题等级**: P1 - 重要

```python
import asyncio
import aiohttp
from concurrent.futures import ThreadPoolExecutor

# 低效：同步IO操作
def fetch_user_data_sync(user_ids):
    results = []
    for user_id in user_ids:
        response = requests.get(f"/api/users/{user_id}")
        results.append(response.json())
    return results

# 高效：异步IO操作
async def fetch_user_data_async(user_ids):
    async with aiohttp.ClientSession() as session:
        tasks = []
        for user_id in user_ids:
            task = fetch_single_user(session, user_id)
            tasks.append(task)
        results = await asyncio.gather(*tasks)
        return results

async def fetch_single_user(session, user_id):
    async with session.get(f"/api/users/{user_id}") as response:
        return await response.json()

# 混合异步：CPU密集型任务使用线程池
async def process_data_hybrid(data_list):
    loop = asyncio.get_event_loop()
    with ThreadPoolExecutor(max_workers=4) as executor:
        tasks = []
        for data in data_list:
            task = loop.run_in_executor(executor, cpu_intensive_task, data)
            tasks.append(task)
        results = await asyncio.gather(*tasks)
        return results
```

#### Java并发优化
**问题等级**: P1 - 重要

```java
@Service
public class DataProcessingService {
    
    private final ExecutorService executorService = 
            Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());
    
    // 低效：串行处理
    public List<ProcessedData> processDataSequential(List<RawData> dataList) {
        return dataList.stream()
                .map(this::expensiveProcessing)
                .collect(Collectors.toList());
    }
    
    // 高效：并行处理
    public List<ProcessedData> processDataParallel(List<RawData> dataList) {
        return dataList.parallelStream()
                .map(this::expensiveProcessing)
                .collect(Collectors.toList());
    }
    
    // 异步处理
    @Async
    public CompletableFuture<ProcessedData> processDataAsync(RawData data) {
        ProcessedData result = expensiveProcessing(data);
        return CompletableFuture.completedFuture(result);
    }
    
    // 批量异步处理
    public CompletableFuture<List<ProcessedData>> processBatchAsync(List<RawData> dataList) {
        List<CompletableFuture<ProcessedData>> futures = dataList.stream()
                .map(this::processDataAsync)
                .collect(Collectors.toList());
                
        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenApply(v -> futures.stream()
                        .map(CompletableFuture::join)
                        .collect(Collectors.toList()));
    }
}
```

---

### 2. 内存管理优化

#### 对象池模式
**问题等级**: P2 - 一般

**Java对象池实现**:
```java
public class DatabaseConnectionPool {
    private final Queue<Connection> pool = new ConcurrentLinkedQueue<>();
    private final AtomicInteger currentSize = new AtomicInteger(0);
    private final int maxSize;
    
    public DatabaseConnectionPool(int maxSize) {
        this.maxSize = maxSize;
    }
    
    public Connection borrowConnection() throws SQLException {
        Connection connection = pool.poll();
        if (connection == null && currentSize.get() < maxSize) {
            connection = createNewConnection();
            currentSize.incrementAndGet();
        }
        return connection;
    }
    
    public void returnConnection(Connection connection) {
        if (connection != null && isValid(connection)) {
            pool.offer(connection);
        } else {
            currentSize.decrementAndGet();
        }
    }
    
    private Connection createNewConnection() throws SQLException {
        return DriverManager.getConnection(url, username, password);
    }
    
    private boolean isValid(Connection connection) {
        try {
            return connection != null && !connection.isClosed();
        } catch (SQLException e) {
            return false;
        }
    }
}
```

#### 内存泄漏防护
**问题等级**: P0 - 严重

**Python内存管理**:
```python
import weakref
from typing import Dict, List

class EventManager:
    def __init__(self):
        # 使用弱引用避免内存泄漏
        self._listeners: Dict[str, List[weakref.ref]] = {}
    
    def add_listener(self, event_type: str, listener):
        if event_type not in self._listeners:
            self._listeners[event_type] = []
        
        # 使用弱引用，当对象被删除时自动清理
        weak_ref = weakref.ref(listener, self._cleanup_listener)
        self._listeners[event_type].append(weak_ref)
    
    def _cleanup_listener(self, weak_ref):
        # 清理已失效的弱引用
        for listeners in self._listeners.values():
            if weak_ref in listeners:
                listeners.remove(weak_ref)
    
    def notify(self, event_type: str, data):
        if event_type in self._listeners:
            # 清理失效的引用并通知有效的监听器
            valid_listeners = []
            for weak_ref in self._listeners[event_type]:
                listener = weak_ref()
                if listener is not None:
                    valid_listeners.append(weak_ref)
                    listener.handle_event(data)
            
            self._listeners[event_type] = valid_listeners
```

---

## 前端性能优化

### 1. 资源优化

#### 图片优化
**问题等级**: P2 - 一般

```python
from PIL import Image
import io

def optimize_image(image_data: bytes, max_width: int = 1200, quality: int = 85) -> bytes:
    """优化图片大小和质量"""
    with Image.open(io.BytesIO(image_data)) as img:
        # 转换为RGB（如果是RGBA）
        if img.mode in ('RGBA', 'LA'):
            background = Image.new('RGB', img.size, (255, 255, 255))
            background.paste(img, mask=img.split()[-1] if img.mode == 'RGBA' else None)
            img = background
        
        # 调整尺寸
        if img.width > max_width:
            ratio = max_width / img.width
            new_height = int(img.height * ratio)
            img = img.resize((max_width, new_height), Image.Lanczos)
        
        # 保存优化后的图片
        output = io.BytesIO()
        img.save(output, format='JPEG', quality=quality, optimize=True)
        return output.getvalue()
```

#### 数据压缩
**问题等级**: P2 - 一般

```python
import gzip
import json
from flask import Flask, request, jsonify

app = Flask(__name__)

def compress_response(data):
    """压缩响应数据"""
    json_data = json.dumps(data)
    
    # 检查是否支持gzip
    if 'gzip' in request.headers.get('Accept-Encoding', ''):
        compressed_data = gzip.compress(json_data.encode('utf-8'))
        response = app.response_class(
            compressed_data,
            mimetype='application/json',
            headers={'Content-Encoding': 'gzip'}
        )
        return response
    
    return jsonify(data)

@app.route('/api/large-data')
def get_large_data():
    large_data = generate_large_dataset()
    return compress_response(large_data)
```
